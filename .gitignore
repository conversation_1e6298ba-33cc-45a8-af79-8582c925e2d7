# 環境變數檔案
.env

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虛擬環境
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 日誌檔案
*.log
logs/*.log

# 測試檔案和結果
test_*.py
*_test.py
test_results/
system_prompt_test_results/
id_analysis_results/

# 臨時檔案
*.tmp
*.temp
.DS_Store
Thumbs.db

# 敏感資料
*.key
*.pem
*.p12
*.pfx

# 備份檔案
*.bak
*.backup
*.old

# 輸出檔案
output/
results/
exports/

# 快取
.cache/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# 系統檔案
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
