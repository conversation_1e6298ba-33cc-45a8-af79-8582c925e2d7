# 多功能資料處理系統

這是一個整合多種 AI 技術的資料處理系統，包含寫作風格分析和身分證件處理功能。

## 🚀 功能特色

### 📝 寫作風格分析系統
- **智能文章爬取**：自動從網站爬取文章內容
- **深度風格分析**：使用 GPT-4 分析寫作風格、語調、用詞習慣
- **System Prompt 生成**：自動生成可用於 AI 助手的風格模板
- **多作者比較**：支援多個作者的風格對比分析

### 🆔 身分證件處理系統
- **三方 AI 比較**：整合 Google Gemini、OpenAI GPT-4o、AWS Claude
- **正反面完整支援**：
  - 正面：姓名、身分證號碼、出生日期、發證日期、性別
  - 背面：父親、母親、配偶、役別、出生地、住址
- **智能面別檢測**：自動識別正面或背面
- **多重驗證**：三方 AI 結果比較，提高準確率
- **標準化 API**：提供完整的 RESTful API 介面

## 🎯 核心優勢

- ✅ **高準確率**：正面欄位 100%，背面欄位 83.3%
- ✅ **多 AI 支援**：Gemini + OpenAI + Claude 三方比較
- ✅ **完整欄位**：支援台灣身分證正反面所有欄位
- ✅ **智能檢測**：自動識別正反面
- ✅ **批次處理**：支援大量圖片處理
- ✅ **API 服務**：完整的 REST API

## 🚀 快速開始

### 1. 環境設定
```bash
# 安裝依賴
pip install -r requirements.txt

# 設定環境變數
cp .env.example .env
# 編輯 .env 檔案，填入你的 API 金鑰
```

### 2. 身分證分析
```bash
# 分析正面（姓名、身分證號碼、出生日期、發證日期、性別）
python id_card_analyzer.py --front 正面.png

# 分析背面（父親、母親、配偶、役別、出生地、住址）
python id_card_analyzer.py --back 背面.png

# 完整分析（正反面合併）
python id_card_analyzer.py --both 正面.png 背面.png

# 三方 AI 比較
python id_analyzer.py 正面.png --compare
```

### 3. 存摺分析
```bash
# 分析存摺（帳戶名稱、存摺帳號）
python passbook_analyzer.py 存摺.jpg

# API 測試
python test_passbook_api.py 存摺.jpg

# 簡單端點測試
python test_passbook_simple.py
```

### 4. 寫作風格分析
```bash
# 執行風格分析
python analyzer.py
```

### 5. API 服務
```bash
# 啟動伺服器
python api_server.py

# 訪問 API 文件
# http://localhost:8000/docs
```

## 📁 專案結構

詳細的專案結構請參考 [專案結構.md](專案結構.md)

## 📖 詳細說明

請參考 [使用說明.md](使用說明.md) 獲取完整的使用指南。

## 🔑 API 金鑰需求

```env
# OpenAI API (用於寫作風格分析和身分證辨識)
OPENAI_API_KEY=your_openai_api_key_here

# Google Gemini API (用於身分證辨識)
GEMINI_API_KEY=your_gemini_api_key_here

# AWS Bedrock Claude API (用於身分證辨識)
AWS_ACCESS_KEY_ID=your_aws_access_key_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here
AWS_DEFAULT_REGION=us-east-1
```

## 💡 使用建議

1. **身分證分析**：建議主要使用 Gemini，準確率最高
2. **重要文件**：使用三方比較功能進行多重驗證
3. **圖片品質**：確保圖片清晰，解析度 > 400x300
4. **批次處理**：大量圖片可使用批次模式

## 🆘 故障排除

1. **API 錯誤**：檢查 API 金鑰是否正確設定
2. **圖片錯誤**：確認圖片格式和大小符合要求
3. **依賴問題**：重新安裝 requirements.txt 中的套件

## 📊 系統需求

- Python 3.8+
- 相關 API 金鑰 (OpenAI, Google Gemini, AWS)
- 網路連線

## 📄 授權

MIT License