#!/usr/bin/env python3
"""
寫作風格分析程式
功能：讀取已爬取的文章資料，使用 GPT 分析寫作風格並產生 System Prompt
"""

import json
import os
import requests
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional
import logging

# 設定日誌
os.makedirs('logs', exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/analyzer.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class StyleAnalyzer:
    """寫作風格分析器"""
    
    def __init__(self, articles_dir: str = "articles", output_dir: str = "analysis_results"):
        """
        初始化分析器
        
        Args:
            articles_dir: 文章資料目錄
            output_dir: 分析結果輸出目錄
        """
        self.articles_dir = Path(articles_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # API 設定
        self.openai_api_key = os.getenv('OPENAI_API_KEY')
        
        # 檢查 API 金鑰
        if not self.openai_api_key:
            logger.warning("未設定 OPENAI_API_KEY 環境變數")
    
    def load_articles(self) -> Optional[Dict[str, List[Dict]]]:
        """載入已爬取的文章資料"""
        articles_file = self.articles_dir / "articles_data.json"
        
        if not articles_file.exists():
            logger.error(f"找不到文章資料檔案: {articles_file}")
            print(f"❌ 找不到文章資料檔案: {articles_file}")
            print("請先執行 crawler.py 爬取文章")
            return None
        
        try:
            with open(articles_file, 'r', encoding='utf-8') as f:
                articles = json.load(f)
            
            logger.info(f"成功載入文章資料")
            for author, article_list in articles.items():
                logger.info(f"{author}: {len(article_list)} 篇文章")
            
            return articles
            
        except Exception as e:
            logger.error(f"載入文章資料失敗: {e}")
            return None
    
    def analyze_writing_style(self, articles: Dict[str, List[Dict]]) -> Dict[str, str]:
        """使用 GPT 分析寫作風格"""
        if not self.openai_api_key:
            logger.error("OpenAI API 金鑰未設定，無法進行風格分析")
            print("❌ 未設定 OPENAI_API_KEY，無法進行風格分析")
            print("請在 .env 檔案中設定 OPENAI_API_KEY")
            return {}
        
        logger.info("開始分析寫作風格...")
        style_analyses = {}
        
        for author, article_list in articles.items():
            if not article_list:
                continue
                
            print(f"🔍 分析 {author.replace('文章連結', '').strip()} 的寫作風格...")
            
            # 合併所有文章內容
            combined_content = "\n\n".join([
                f"文章標題: {article['title']}\n內容: {article['content']}"
                for article in article_list
            ])
            
            # 限制內容長度（gpt-4o 有更大的 context，但仍需要限制）
            max_chars = 80000  # 約 20k tokens
            if len(combined_content) > max_chars:
                # 智能截斷：保留前面和後面的內容
                half_size = max_chars // 2
                combined_content = (
                    combined_content[:half_size] +
                    "\n\n... [中間內容已省略] ...\n\n" +
                    combined_content[-half_size:]
                )
            
            style_analysis = self._analyze_single_author_style(author, combined_content)
            if style_analysis:
                style_analyses[author] = style_analysis
        
        return style_analyses
    
    def _analyze_single_author_style(self, author: str, content: str) -> Optional[str]:
        """分析單個作者的寫作風格"""
        try:
            headers = {
                'Authorization': f'Bearer {self.openai_api_key}',
                'Content-Type': 'application/json'
            }
            
            # 根據作者名稱客製化分析重點
            author_name = author.replace('文章連結', '').strip()
            
            if '小編A' in author:
                analysis_focus = """
特別注意分析：
- 時尚購物類文章的寫作特色
- 商品推薦的表達方式
- 價格資訊的呈現風格
- 對讀者的稱呼方式（如「女孩們」等）
- 購物慾望的激發技巧
- 商品描述的用詞習慣
"""
            elif '小編JU' in author:
                analysis_focus = """
特別注意分析：
- 美妝保養類文章的寫作特色
- 產品功效的描述方式
- 品牌和明星代言的提及風格
- 使用體驗的表達方式
- 專業美妝知識的融入程度
- 讀者互動的語氣特點
"""
            else:
                analysis_focus = """
特別注意分析：
- 該作者的獨特寫作特色
- 專業領域的表達方式
- 與讀者互動的風格
"""
            
            prompt = f"""請深入分析以下{author_name}的文章內容，並產生兩個部分的輸出：

## 第一部分：風格特徵分析
請從以下角度詳細分析：
1. **語言風格**：正式/非正式程度、親切/專業平衡、語調特色
2. **句式特點**：長短句比例、常用句型、標點符號使用習慣
3. **詞彙選擇**：專業術語使用、口語化程度、形容詞偏好
4. **文章結構**：段落組織方式、資訊呈現邏輯、標題風格
5. **語氣情感**：情感表達方式、讀者互動風格、說服技巧
6. **特殊習慣**：口頭禪、慣用表達、獨特用詞

{analysis_focus}

## 第二部分：System Prompt
基於以上分析，產生一個完整的 system prompt，格式如下：

```
你是{author_name}，一位專業的[領域]內容創作者。請以{author_name}的寫作風格來撰寫內容。

你的寫作風格特點：
1. [語言風格特點]
2. [句式和表達特點]
3. [詞彙使用特點]
4. [文章結構特點]
5. [語氣情感特點]
6. [其他獨特特徵]

請始終保持這種風格來回應和創作內容。
```

文章內容：
{content}
"""
            
            data = {
                'model': 'gpt-4o',
                'messages': [
                    {
                        'role': 'system',
                        'content': '你是一個專業的文本風格分析師，擅長分析寫作風格並產生相應的寫作指導 prompt。'
                    },
                    {
                        'role': 'user',
                        'content': prompt
                    }
                ],
                'max_tokens': 2000,
                'temperature': 0.3
            }
            
            response = requests.post(
                'https://api.openai.com/v1/chat/completions',
                headers=headers,
                json=data,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                analysis = result['choices'][0]['message']['content']
                logger.info(f"完成 {author} 的風格分析")
                return analysis
            else:
                logger.error(f"OpenAI API 錯誤 {response.status_code}: {response.text}")
                
        except Exception as e:
            logger.error(f"分析 {author} 風格時發生錯誤: {e}")
        
        return None
    
    def save_analysis_results(self, analyses: Dict[str, str]) -> None:
        """儲存分析結果"""
        if not analyses:
            logger.info("沒有分析結果需要儲存")
            return
            
        logger.info("儲存分析結果...")
        
        # 儲存個別分析
        for author, analysis in analyses.items():
            author_name = author.replace('文章連結', '').strip()
            
            # 儲存完整分析
            analysis_filename = f"{author_name}_complete_analysis.md"
            analysis_filepath = self.output_dir / analysis_filename
            
            analysis_content = f"""# {author_name} 完整寫作風格分析

**分析時間:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

{analysis}

---

## 使用說明

1. **風格特徵分析**：了解該作者的寫作特色
2. **System Prompt**：可直接複製使用的 GPT 指令
3. **應用場景**：適用於需要模仿該作者風格的內容創作

"""
            
            with open(analysis_filepath, 'w', encoding='utf-8') as f:
                f.write(analysis_content)
            
            # 提取並單獨儲存 System Prompt
            system_prompt = self._extract_system_prompt(analysis)
            if system_prompt:
                prompt_filename = f"{author_name}_system_prompt.txt"
                prompt_filepath = self.output_dir / prompt_filename
                
                with open(prompt_filepath, 'w', encoding='utf-8') as f:
                    f.write(system_prompt)
                
                logger.info(f"儲存 System Prompt: {prompt_filepath}")
                print(f"✅ {author_name} System Prompt: {prompt_filename}")
            
            logger.info(f"儲存完整分析: {analysis_filepath}")
        
        # 儲存合併的分析結果
        combined_file = self.output_dir / "all_style_analyses.md"
        with open(combined_file, 'w', encoding='utf-8') as f:
            f.write("# 所有作者寫作風格分析\n\n")
            f.write(f"**分析時間:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write("## 概述\n\n")
            f.write("本文件包含所有作者的寫作風格分析，每個作者都有獨特的風格特徵和對應的 System Prompt。\n\n")
            
            for author, analysis in analyses.items():
                author_name = author.replace('文章連結', '').strip()
                f.write(f"## {author_name}\n\n")
                f.write(f"{analysis}\n\n")
                f.write("---\n\n")
        
        logger.info(f"儲存合併分析: {combined_file}")
        print(f"📄 合併分析報告: all_style_analyses.md")
    
    def _extract_system_prompt(self, analysis: str) -> Optional[str]:
        """從分析結果中提取 System Prompt"""
        try:
            # 尋找 System Prompt 部分
            lines = analysis.split('\n')
            in_prompt_section = False
            prompt_lines = []
            
            for line in lines:
                if '## 第二部分：System Prompt' in line or 'System Prompt' in line:
                    in_prompt_section = True
                    continue
                elif line.startswith('##') and in_prompt_section:
                    break
                elif in_prompt_section and line.strip():
                    # 移除 markdown 程式碼區塊標記
                    if line.strip() == '```' or line.strip().startswith('```'):
                        continue
                    prompt_lines.append(line)
            
            if prompt_lines:
                return '\n'.join(prompt_lines).strip()
        except Exception as e:
            logger.warning(f"提取 System Prompt 失敗: {e}")
        
        return None
    
    def run_analysis(self) -> None:
        """執行完整的分析流程"""
        logger.info("開始執行寫作風格分析...")
        
        try:
            # 1. 載入文章資料
            articles = self.load_articles()
            if not articles:
                return
            
            # 2. 分析寫作風格
            style_analyses = self.analyze_writing_style(articles)
            
            # 3. 儲存分析結果
            if style_analyses:
                self.save_analysis_results(style_analyses)
                
                print(f"\n✅ 分析完成！")
                print(f"📁 結果儲存在: {self.output_dir}")
                print(f"📝 可直接使用的 System Prompt 檔案:")
                for author in style_analyses.keys():
                    author_name = author.replace('文章連結', '').strip()
                    print(f"   - {author_name}_system_prompt.txt")
            else:
                print("❌ 分析失敗，請檢查 API 設定")
            
            logger.info("寫作風格分析完成！")
            
        except Exception as e:
            logger.error(f"分析過程中發生錯誤: {e}")
            raise

def main():
    """主函數"""
    # 載入環境變數
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        logger.warning("python-dotenv 未安裝，跳過 .env 檔案載入")
    
    print("🔍 寫作風格分析程式")
    print("=" * 50)
    
    # 建立分析器並執行
    analyzer = StyleAnalyzer()
    analyzer.run_analysis()

if __name__ == "__main__":
    main()
