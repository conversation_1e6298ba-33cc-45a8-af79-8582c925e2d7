#!/usr/bin/env python3
"""
OpenAI Chat Completions API 管理器
用於整合到 FastAPI 應用程式中，使用 Chat Completions 而非已棄用的 Assistants API
"""

import os
import uuid
import json
from typing import Dict, Any
from dotenv import load_dotenv
from openai import OpenAI
import logging

# 設定日誌
logger = logging.getLogger(__name__)

class AssistantsManager:
    """OpenAI Chat Completions API 管理器"""

    def __init__(self, api_key: str = None):
        """
        初始化 Chat Completions API 管理器

        Args:
            api_key: OpenAI API 金鑰，如果不提供則從環境變數讀取
        """
        # 載入環境變數
        load_dotenv()

        # 初始化 OpenAI 客戶端
        self.api_key = api_key or os.getenv('OPENAI_API_KEY')
        if not self.api_key:
            raise ValueError("未設定 OPENAI_API_KEY")

        self.client = OpenAI(api_key=self.api_key)

        # 儲存對話歷史 (thread_id -> messages)
        self.conversation_history = {}

        # 小編系統提示詞
        self.xiabian_prompts = {
            "小編JU": """你是小編JU，一位專精於美妝保養內容創作的專業小編。

你的特色：
- 語言風格輕鬆親切、活潑熱情
- 就像和好朋友聊天一樣自然
- 多使用生活化的語言和表情符號
- 讓讀者感受到溫暖和親近感
- 專精於美妝保養領域的知識分享

請以 Markdown 格式回應，並保持你獨特的寫作風格。""",

            "小編A": """你是小編A，一位專精於內容創作的專業小編。

你的特色：
- 專業、深度的寫作風格
- 展現專業知識和獨特見解
- 文章結構清晰、邏輯嚴謹
- 適合追求高品質內容的讀者
- 具有廣泛的知識背景

請以 Markdown 格式回應，並保持你專業的寫作風格。"""
        }

        logger.info("Chat Completions 管理器初始化完成")

    def get_or_create_conversation(self, thread_id: str = None) -> str:
        """
        取得或建立對話

        Args:
            thread_id: 對話 ID，如果不提供則建立新的

        Returns:
            對話 ID
        """
        if thread_id is None:
            thread_id = str(uuid.uuid4())
            self.conversation_history[thread_id] = []
            logger.info(f"建立新對話: {thread_id}")
        elif thread_id not in self.conversation_history:
            self.conversation_history[thread_id] = []
            logger.info(f"初始化對話: {thread_id}")

        return thread_id

    def add_message_to_conversation(self, thread_id: str, role: str, content: str):
        """
        新增訊息到對話歷史

        Args:
            thread_id: 對話 ID
            role: 角色 (user/assistant/system)
            content: 訊息內容
        """
        if thread_id not in self.conversation_history:
            self.conversation_history[thread_id] = []

        self.conversation_history[thread_id].append({
            "role": role,
            "content": content
        })

        # 限制對話歷史長度，避免 token 過多
        max_messages = 20
        if len(self.conversation_history[thread_id]) > max_messages:
            # 保留系統訊息和最近的訊息
            system_messages = [msg for msg in self.conversation_history[thread_id] if msg["role"] == "system"]
            recent_messages = self.conversation_history[thread_id][-max_messages:]
            self.conversation_history[thread_id] = system_messages + recent_messages

    def chat_with_xiabian(self, xiabian_name: str, user_message: str, thread_id: str = None) -> Dict[str, Any]:
        """
        與指定的小編對話

        Args:
            xiabian_name: 小編名稱 ("小編JU" 或 "小編A")
            user_message: 用戶訊息
            thread_id: 對話 ID，如果不提供則建立新的

        Returns:
            對話結果
        """
        try:
            # 檢查小編是否存在
            if xiabian_name not in self.xiabian_prompts:
                return {
                    "success": False,
                    "error": f"找不到小編 '{xiabian_name}'，可用的小編: {list(self.xiabian_prompts.keys())}"
                }

            # 取得或建立對話
            thread_id = self.get_or_create_conversation(thread_id)

            # 準備訊息列表
            messages = []

            # 如果是新對話，加入系統提示詞
            if not self.conversation_history[thread_id]:
                system_prompt = self.xiabian_prompts[xiabian_name]
                messages.append({"role": "system", "content": system_prompt})
                self.add_message_to_conversation(thread_id, "system", system_prompt)
            else:
                # 使用現有對話歷史
                messages = self.conversation_history[thread_id].copy()

            # 加入用戶訊息
            messages.append({"role": "user", "content": user_message})
            self.add_message_to_conversation(thread_id, "user", user_message)

            # 調用 Chat Completions API
            response = self.client.chat.completions.create(
                model="gpt-4o",
                messages=messages,
                max_tokens=2000,
                temperature=0.7
            )

            # 取得回應
            assistant_response = response.choices[0].message.content

            # 儲存助手回應到對話歷史
            self.add_message_to_conversation(thread_id, "assistant", assistant_response)

            logger.info(f"{xiabian_name} 對話成功，Thread: {thread_id}")

            return {
                "success": True,
                "response": assistant_response,
                "thread_id": thread_id,
                "run_id": str(uuid.uuid4()),  # 為了相容性提供一個假的 run_id
                "xiabian_name": xiabian_name
            }

        except Exception as e:
            logger.error(f"{xiabian_name} 對話失敗: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def get_available_xiabian(self) -> Dict[str, str]:
        """
        取得可用的小編列表

        Returns:
            小編名稱和描述的字典
        """
        return {
            "小編JU": "專精於美妝保養內容創作，語言風格輕鬆親切、活潑熱情",
            "小編A": "專精於內容創作，具有獨特的寫作風格和專業知識"
        }
