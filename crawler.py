#!/usr/bin/env python3
"""
文章爬蟲程式
功能：從 Excel 檔案讀取連結，爬取網頁內容並儲存為 Markdown 檔案
"""

import pandas as pd
import requests
import os
import time
import json
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Optional
import logging
from bs4 import BeautifulSoup
import html2text

# 設定日誌
os.makedirs('logs', exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/crawler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ArticleCrawler:
    """文章爬蟲類別"""
    
    def __init__(self, excel_file: str, output_dir: str = "articles"):
        """
        初始化爬蟲
        
        Args:
            excel_file: Excel 檔案路徑
            output_dir: 輸出目錄
        """
        self.excel_file = excel_file
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # HTML 轉 Markdown 轉換器
        self.html_converter = html2text.HTML2Text()
        self.html_converter.ignore_links = False
        self.html_converter.ignore_images = True
    
    def read_excel_links(self) -> Dict[str, List[str]]:
        """讀取 Excel 檔案中的文章連結"""
        logger.info(f"讀取 Excel 檔案: {self.excel_file}")
        
        try:
            df = pd.read_excel(self.excel_file)
            
            links = {}
            for column in df.columns:
                column_links = df[column].dropna().tolist()
                column_links = [link.strip() for link in column_links if link.strip()]
                links[column] = column_links
                logger.info(f"{column}: 找到 {len(column_links)} 個連結")
            
            return links
            
        except Exception as e:
            logger.error(f"讀取 Excel 檔案失敗: {e}")
            raise
    
    def crawl_article(self, url: str) -> Optional[Dict]:
        """
        爬取單篇文章
        
        Args:
            url: 文章 URL
            
        Returns:
            包含文章內容的字典
        """
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            
            response = requests.get(url, headers=headers, timeout=30)
            response.raise_for_status()
            
            # 解析 HTML
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 提取標題
            title = ""
            title_tags = soup.find_all(['h1', 'title'])
            if title_tags:
                title = title_tags[0].get_text().strip()
            
            # 移除不需要的元素
            for element in soup(['script', 'style', 'nav', 'header', 'footer', 'aside']):
                element.decompose()
            
            # 尋找主要內容區域
            main_content = None
            
            # 常見的內容容器選擇器
            content_selectors = [
                'article',
                '.content',
                '.post-content',
                '.entry-content',
                '.article-content',
                'main',
                '#content',
                '.main-content'
            ]
            
            for selector in content_selectors:
                main_content = soup.select_one(selector)
                if main_content:
                    break
            
            # 如果找不到特定容器，使用整個 body
            if not main_content:
                main_content = soup.find('body')
            
            if not main_content:
                logger.warning(f"無法找到主要內容: {url}")
                return None
            
            # 轉換為 Markdown
            html_content = str(main_content)
            markdown_content = self.html_converter.handle(html_content)
            
            # 清理 Markdown 內容
            markdown_content = self._clean_markdown(markdown_content)
            
            return {
                'title': title or '無標題',
                'content': markdown_content,
                'url': url,
                'crawled_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"爬取文章失敗 {url}: {e}")
            return None
    
    def _clean_markdown(self, content: str) -> str:
        """清理 Markdown 內容"""
        lines = content.split('\n')
        cleaned_lines = []
        
        for line in lines:
            line = line.strip()
            # 跳過空行過多的情況
            if line or (cleaned_lines and cleaned_lines[-1]):
                cleaned_lines.append(line)
        
        # 移除連續的空行
        result = []
        prev_empty = False
        for line in cleaned_lines:
            if line:
                result.append(line)
                prev_empty = False
            elif not prev_empty:
                result.append(line)
                prev_empty = True
        
        return '\n'.join(result)
    
    def crawl_all_articles(self, links: Dict[str, List[str]]) -> Dict[str, List[Dict]]:
        """爬取所有文章"""
        all_articles = {}
        
        for author, urls in links.items():
            logger.info(f"開始爬取 {author} 的文章...")
            articles = []
            
            for i, url in enumerate(urls, 1):
                logger.info(f"爬取第 {i}/{len(urls)} 篇文章: {url}")
                
                article_data = self.crawl_article(url)
                if article_data:
                    articles.append(article_data)
                    logger.info(f"成功爬取: {article_data['title']}")
                else:
                    logger.warning(f"爬取失敗: {url}")
                
                # 避免過於頻繁的請求
                time.sleep(2)
            
            all_articles[author] = articles
            logger.info(f"{author} 完成，成功爬取 {len(articles)} 篇文章")
        
        return all_articles
    
    def save_articles(self, articles: Dict[str, List[Dict]]) -> None:
        """儲存文章到檔案"""
        logger.info("開始儲存文章...")
        
        # 儲存 JSON 格式（供分析程式使用）
        articles_json = self.output_dir / "articles_data.json"
        with open(articles_json, 'w', encoding='utf-8') as f:
            json.dump(articles, f, ensure_ascii=False, indent=2)
        logger.info(f"儲存 JSON 資料: {articles_json}")
        
        # 儲存 Markdown 格式（供人類閱讀）
        for author, article_list in articles.items():
            author_name = author.replace('文章連結', '').strip()
            author_dir = self.output_dir / author_name
            author_dir.mkdir(exist_ok=True)
            
            # 儲存個別文章
            for i, article in enumerate(article_list, 1):
                filename = f"article_{i:02d}.md"
                filepath = author_dir / filename
                
                content = f"""# {article['title']}

**來源:** {article['url']}  
**爬取時間:** {article['crawled_at']}

---

{article['content']}
"""
                
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(content)
            
            # 儲存合併的文章集合
            combined_file = author_dir / "all_articles.md"
            with open(combined_file, 'w', encoding='utf-8') as f:
                f.write(f"# {author_name} 所有文章\n\n")
                for i, article in enumerate(article_list, 1):
                    f.write(f"## 文章 {i}: {article['title']}\n\n")
                    f.write(f"**來源:** {article['url']}\n\n")
                    f.write(f"{article['content']}\n\n")
                    f.write("---\n\n")
            
            logger.info(f"儲存 {author_name}: {len(article_list)} 篇文章")
        
        # 儲存爬取摘要
        summary = {
            'crawl_time': datetime.now().isoformat(),
            'total_articles': sum(len(articles) for articles in articles.values()),
            'authors': {
                author.replace('文章連結', '').strip(): len(article_list) 
                for author, article_list in articles.items()
            }
        }
        
        summary_file = self.output_dir / "crawl_summary.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        logger.info(f"儲存爬取摘要: {summary_file}")
    
    def run_crawl(self) -> None:
        """執行完整的爬取流程"""
        logger.info("開始執行文章爬取...")
        
        try:
            # 1. 讀取 Excel 連結
            links = self.read_excel_links()
            
            # 2. 爬取所有文章
            articles = self.crawl_all_articles(links)
            
            # 3. 儲存文章
            self.save_articles(articles)
            
            logger.info("文章爬取完成！")
            print(f"\n✅ 爬取完成！")
            print(f"📁 結果儲存在: {self.output_dir}")
            print(f"📊 爬取摘要: {self.output_dir}/crawl_summary.json")
            print(f"📄 文章資料: {self.output_dir}/articles_data.json")
            
        except Exception as e:
            logger.error(f"爬取過程中發生錯誤: {e}")
            raise

def main():
    """主函數"""
    # 設定檔案路徑
    excel_file = "小編文章.xlsx"
    output_dir = "articles"
    
    # 檢查 Excel 檔案是否存在
    if not os.path.exists(excel_file):
        print(f"❌ 錯誤: 找不到 Excel 檔案 '{excel_file}'")
        return
    
    print("📰 文章爬蟲程式")
    print("=" * 50)
    
    # 建立爬蟲並執行
    crawler = ArticleCrawler(excel_file, output_dir)
    crawler.run_crawl()

if __name__ == "__main__":
    main()
