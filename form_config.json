{"endpoints": {"primary": {"url": "https://api.example.com/submit-form", "method": "POST", "timeout": 30, "retry_count": 3, "headers": {}}, "backup": {"url": "https://backup-api.example.com/submit-form", "method": "POST", "timeout": 30, "retry_count": 2, "headers": {}}}, "field_mapping": {"name": "customer_name", "id_number": "national_id", "address": "customer_address", "confidence": "ocr_confidence", "timestamp": "processed_at"}, "validation": {"required_fields": ["name", "id_number"], "min_confidence": 0.7}, "webhook": {"enabled": false, "url": "", "secret": ""}}