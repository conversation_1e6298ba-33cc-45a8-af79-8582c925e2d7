#!/usr/bin/env python3
"""
表單提交器
功能：將身分證分析結果的 JSON 資料拋送到表單系統
"""

import json
import requests
import os
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
import logging
import time

# 設定日誌
os.makedirs('logs', exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/form_submitter.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class FormSubmitter:
    """表單提交器"""
    
    def __init__(self, config_file: str = "form_config.json"):
        """
        初始化表單提交器
        
        Args:
            config_file: 表單設定檔案路徑
        """
        self.config_file = config_file
        self.config = self._load_config()
        self.session = requests.Session()
        
        # 設定預設 headers
        self.session.headers.update({
            'User-Agent': 'ID-Card-Form-Submitter/1.0',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
        
        logger.info("表單提交器初始化完成")
    
    def _load_config(self) -> Dict[str, Any]:
        """
        載入表單設定
        
        Returns:
            設定字典
        """
        config_path = Path(self.config_file)
        
        if not config_path.exists():
            # 建立預設設定檔案
            default_config = {
                "endpoints": {
                    "primary": {
                        "url": "https://api.example.com/submit-form",
                        "method": "POST",
                        "timeout": 30,
                        "retry_count": 3,
                        "headers": {}
                    },
                    "backup": {
                        "url": "https://backup-api.example.com/submit-form",
                        "method": "POST",
                        "timeout": 30,
                        "retry_count": 2,
                        "headers": {}
                    }
                },
                "field_mapping": {
                    "name": "customer_name",
                    "id_number": "national_id",
                    "address": "customer_address",
                    "confidence": "ocr_confidence",
                    "timestamp": "processed_at"
                },
                "validation": {
                    "required_fields": ["name", "id_number"],
                    "min_confidence": 0.7
                },
                "webhook": {
                    "enabled": False,
                    "url": "",
                    "secret": ""
                }
            }
            
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, ensure_ascii=False, indent=2)
            
            logger.info(f"建立預設設定檔案: {config_path}")
            return default_config
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            logger.info(f"載入設定檔案: {config_path}")
            return config
        except Exception as e:
            logger.error(f"載入設定檔案失敗: {e}")
            raise
    
    def validate_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        驗證資料
        
        Args:
            data: 要驗證的資料
            
        Returns:
            驗證結果
        """
        validation_config = self.config.get('validation', {})
        required_fields = validation_config.get('required_fields', [])
        min_confidence = validation_config.get('min_confidence', 0.0)
        
        result = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        # 檢查必要欄位
        for field in required_fields:
            if not data.get(field):
                result['valid'] = False
                result['errors'].append(f"缺少必要欄位: {field}")
        
        # 檢查信心度
        confidence = data.get('confidence', 0)
        if confidence < min_confidence:
            result['warnings'].append(f"信心度過低: {confidence:.2f} < {min_confidence}")
        
        # 檢查身分證號碼格式
        id_number = data.get('id_number')
        if id_number:
            import re
            if not re.match(r'^[A-Z]\d{9}$', id_number):
                result['valid'] = False
                result['errors'].append(f"身分證號碼格式不正確: {id_number}")
        
        return result
    
    def transform_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        轉換資料格式
        
        Args:
            data: 原始資料
            
        Returns:
            轉換後的資料
        """
        field_mapping = self.config.get('field_mapping', {})
        transformed = {}
        
        for original_field, mapped_field in field_mapping.items():
            if original_field in data:
                transformed[mapped_field] = data[original_field]
        
        # 加入額外的中繼資料
        transformed['submission_id'] = f"ID_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{hash(str(data)) % 10000:04d}"
        transformed['submission_time'] = datetime.now().isoformat()
        transformed['source'] = 'id_card_analyzer'
        
        return transformed
    
    def submit_to_endpoint(self, data: Dict[str, Any], endpoint_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        提交資料到指定端點
        
        Args:
            data: 要提交的資料
            endpoint_config: 端點設定
            
        Returns:
            提交結果
        """
        url = endpoint_config['url']
        method = endpoint_config.get('method', 'POST')
        timeout = endpoint_config.get('timeout', 30)
        retry_count = endpoint_config.get('retry_count', 3)
        custom_headers = endpoint_config.get('headers', {})
        
        # 更新 headers
        headers = self.session.headers.copy()
        headers.update(custom_headers)
        
        for attempt in range(retry_count):
            try:
                logger.info(f"提交資料到 {url} (嘗試 {attempt + 1}/{retry_count})")
                
                if method.upper() == 'POST':
                    response = self.session.post(
                        url,
                        json=data,
                        headers=headers,
                        timeout=timeout
                    )
                elif method.upper() == 'PUT':
                    response = self.session.put(
                        url,
                        json=data,
                        headers=headers,
                        timeout=timeout
                    )
                else:
                    raise ValueError(f"不支援的 HTTP 方法: {method}")
                
                # 檢查回應狀態
                response.raise_for_status()
                
                result = {
                    'success': True,
                    'status_code': response.status_code,
                    'response': response.json() if response.content else {},
                    'endpoint': url,
                    'attempt': attempt + 1
                }
                
                logger.info(f"提交成功: {url} (狀態碼: {response.status_code})")
                return result
                
            except requests.exceptions.RequestException as e:
                logger.warning(f"提交失敗 (嘗試 {attempt + 1}/{retry_count}): {e}")
                
                if attempt < retry_count - 1:
                    # 等待後重試
                    wait_time = 2 ** attempt  # 指數退避
                    time.sleep(wait_time)
                else:
                    # 最後一次嘗試失敗
                    return {
                        'success': False,
                        'error': str(e),
                        'endpoint': url,
                        'attempts': retry_count
                    }
            
            except Exception as e:
                logger.error(f"提交過程發生未預期錯誤: {e}")
                return {
                    'success': False,
                    'error': f"未預期錯誤: {str(e)}",
                    'endpoint': url,
                    'attempt': attempt + 1
                }
    
    def submit_form_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        提交表單資料
        
        Args:
            data: 身分證分析結果資料
            
        Returns:
            提交結果
        """
        logger.info("開始提交表單資料")
        
        # 1. 驗證資料
        validation_result = self.validate_data(data)
        if not validation_result['valid']:
            logger.error(f"資料驗證失敗: {validation_result['errors']}")
            return {
                'success': False,
                'error': 'Data validation failed',
                'validation_errors': validation_result['errors'],
                'validation_warnings': validation_result.get('warnings', [])
            }
        
        if validation_result.get('warnings'):
            logger.warning(f"資料驗證警告: {validation_result['warnings']}")
        
        # 2. 轉換資料格式
        transformed_data = self.transform_data(data)
        
        # 3. 嘗試提交到主要端點
        endpoints = self.config.get('endpoints', {})
        primary_endpoint = endpoints.get('primary')
        
        if primary_endpoint:
            result = self.submit_to_endpoint(transformed_data, primary_endpoint)
            if result['success']:
                logger.info("主要端點提交成功")
                return result
            else:
                logger.warning(f"主要端點提交失敗: {result.get('error')}")
        
        # 4. 嘗試提交到備用端點
        backup_endpoint = endpoints.get('backup')
        if backup_endpoint:
            logger.info("嘗試備用端點")
            result = self.submit_to_endpoint(transformed_data, backup_endpoint)
            if result['success']:
                logger.info("備用端點提交成功")
                return result
            else:
                logger.error(f"備用端點也提交失敗: {result.get('error')}")
        
        # 5. 所有端點都失敗
        return {
            'success': False,
            'error': 'All endpoints failed',
            'data': transformed_data
        }
    
    def send_webhook(self, data: Dict[str, Any], result: Dict[str, Any]) -> None:
        """
        發送 webhook 通知
        
        Args:
            data: 原始資料
            result: 提交結果
        """
        webhook_config = self.config.get('webhook', {})
        if not webhook_config.get('enabled', False):
            return
        
        webhook_url = webhook_config.get('url')
        webhook_secret = webhook_config.get('secret', '')
        
        if not webhook_url:
            logger.warning("Webhook 已啟用但未設定 URL")
            return
        
        try:
            webhook_data = {
                'event': 'form_submission',
                'timestamp': datetime.now().isoformat(),
                'data': data,
                'result': result,
                'secret': webhook_secret
            }
            
            response = self.session.post(
                webhook_url,
                json=webhook_data,
                timeout=10
            )
            
            if response.status_code == 200:
                logger.info("Webhook 發送成功")
            else:
                logger.warning(f"Webhook 發送失敗: {response.status_code}")
                
        except Exception as e:
            logger.error(f"Webhook 發送錯誤: {e}")
    
    def process_analysis_result(self, analysis_result_file: str) -> Dict[str, Any]:
        """
        處理分析結果檔案
        
        Args:
            analysis_result_file: 分析結果檔案路徑
            
        Returns:
            處理結果
        """
        try:
            # 載入分析結果
            with open(analysis_result_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            logger.info(f"載入分析結果: {analysis_result_file}")
            
            # 提交表單資料
            result = self.submit_form_data(data)
            
            # 發送 webhook（如果啟用）
            self.send_webhook(data, result)
            
            # 儲存提交記錄
            self._save_submission_log(data, result)
            
            return result
            
        except Exception as e:
            logger.error(f"處理分析結果失敗: {e}")
            return {
                'success': False,
                'error': f"處理失敗: {str(e)}"
            }
    
    def _save_submission_log(self, data: Dict[str, Any], result: Dict[str, Any]) -> None:
        """
        儲存提交記錄
        
        Args:
            data: 原始資料
            result: 提交結果
        """
        try:
            log_dir = Path("submission_logs")
            log_dir.mkdir(exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            log_file = log_dir / f"submission_{timestamp}.json"
            
            log_data = {
                'timestamp': datetime.now().isoformat(),
                'original_data': data,
                'submission_result': result
            }
            
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(log_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"提交記錄已儲存: {log_file}")
            
        except Exception as e:
            logger.error(f"儲存提交記錄失敗: {e}")
    
    def batch_submit(self, analysis_results_dir: str) -> Dict[str, Any]:
        """
        批次提交分析結果
        
        Args:
            analysis_results_dir: 分析結果目錄
            
        Returns:
            批次提交結果
        """
        results_dir = Path(analysis_results_dir)
        if not results_dir.exists():
            logger.error(f"分析結果目錄不存在: {results_dir}")
            return {'success': False, 'error': 'Directory not found'}
        
        batch_results = {
            'total': 0,
            'successful': 0,
            'failed': 0,
            'results': []
        }
        
        for json_file in results_dir.glob("*.json"):
            logger.info(f"處理檔案: {json_file.name}")
            
            result = self.process_analysis_result(str(json_file))
            batch_results['total'] += 1
            
            if result.get('success'):
                batch_results['successful'] += 1
            else:
                batch_results['failed'] += 1
            
            batch_results['results'].append({
                'file': json_file.name,
                'result': result
            })
        
        logger.info(f"批次提交完成: 總計 {batch_results['total']}, 成功 {batch_results['successful']}, 失敗 {batch_results['failed']}")
        return batch_results

def main():
    """主函數 - 命令列介面"""
    import argparse
    
    parser = argparse.ArgumentParser(description='表單提交器')
    parser.add_argument('input_file', help='分析結果 JSON 檔案路徑')
    parser.add_argument('--config', '-c', default='form_config.json', help='設定檔案路徑')
    parser.add_argument('--batch', '-b', action='store_true', help='批次處理模式')
    
    args = parser.parse_args()
    
    try:
        submitter = FormSubmitter(args.config)
        
        if args.batch:
            # 批次處理
            results = submitter.batch_submit(args.input_file)
            print(f"✅ 批次提交完成")
            print(f"總計: {results['total']}, 成功: {results['successful']}, 失敗: {results['failed']}")
        else:
            # 單檔處理
            result = submitter.process_analysis_result(args.input_file)
            
            if result.get('success'):
                print("✅ 表單提交成功")
                print(f"狀態碼: {result.get('status_code')}")
                print(f"端點: {result.get('endpoint')}")
            else:
                print("❌ 表單提交失敗")
                print(f"錯誤: {result.get('error')}")
                
                if result.get('validation_errors'):
                    print(f"驗證錯誤: {result['validation_errors']}")
    
    except Exception as e:
        logger.error(f"程式執行失敗: {e}")
        print(f"❌ 執行失敗: {e}")

if __name__ == "__main__":
    main()
