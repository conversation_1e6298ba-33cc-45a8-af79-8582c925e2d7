# AI 小編對話系統 - 前端應用程式

一個現代化的檔案上傳與內容生成介面，整合 AI 小編對話系統，能夠分析上傳的檔案（圖片或文字）並生成精彩的部落格內容。

## ✨ 功能特色

### 🎯 核心功能
- **智能檔案上傳**: 支援拖拽上傳，即時進度顯示
- **多格式支援**: 支援圖片檔案 (PNG, JPG, JPEG, WEBP) 和文字檔案 (Markdown, TXT)
- **AI 內容生成**: 基於檔案分析結果自動生成部落格文章
- **多風格小編**: 支援「小編A」和「小編JU」兩種不同的寫作風格
- **即時切換**: 可隨時切換小編風格，內容會重新生成

### 🎨 設計特色
- **現代化 UI**: 採用簡潔、扁平化的設計風格
- **響應式佈局**: 適配不同螢幕尺寸的設備
- **流暢動畫**: 使用 Framer Motion 提供流暢的互動體驗
- **直觀操作**: 清晰的視覺回饋和操作指引

### 🔧 技術特色
- **React 19**: 使用最新版本的 React
- **Vite**: 快速的開發建置工具
- **現代 CSS**: 使用 CSS 變數和現代佈局技術
- **TypeScript 支援**: 完整的類型定義（可選）

## 🚀 快速開始

### 環境需求
- Node.js 18+
- npm 或 yarn
- 後端 FastAPI 服務運行在 `http://localhost:8000`

### 安裝與啟動

1. **安裝依賴**
   ```bash
   npm install
   ```

2. **啟動開發伺服器**
   ```bash
   npm run dev
   ```

3. **開啟瀏覽器**
   訪問 `http://localhost:5173`

### 建置生產版本
```bash
npm run build
npm run preview
```

## 📁 專案結構

```
src/
├── components/           # React 元件
│   ├── FileUpload.jsx   # 檔案上傳元件
│   ├── ContentDisplay.jsx # 內容展示元件
│   ├── XiabianSelector.jsx # 小編選擇器
│   └── components.css   # 元件樣式
├── services/            # API 服務
│   └── api.js          # API 通訊模組
├── utils/              # 工具函數
│   └── testHelpers.js  # 測試輔助函數
├── App.jsx             # 主應用程式元件
├── main.jsx           # 應用程式入口
└── index.css          # 全域樣式
```

## 🎭 小編風格說明

### 小編A
- **特色**: 專業、深度的寫作風格
- **適用**: 追求高品質內容的讀者
- **風格**: 結構清晰、邏輯嚴謹、專業知識豐富

### 小編JU
- **特色**: 輕鬆親切、活潑熱情的語調
- **適用**: 喜歡輕鬆閱讀的讀者
- **風格**: 生活化語言、親近感、溫暖友善

## 🔌 API 整合

應用程式與後端 FastAPI 服務整合，主要使用以下端點：

- `POST /api/v1/analyze` - 檔案分析（圖片）
- `POST /api/v1/analyze-text` - 檔案分析（文字）
- `POST /api/v1/xiabian-a/chat` - 與小編A對話
- `POST /api/v1/xiabian-ju/chat` - 與小編JU對話
- `GET /api/v1/config` - 獲取系統配置
- `GET /health` - 健康檢查

## 📱 支援的檔案格式

- **檔案格式**: PNG, JPG, JPEG, WEBP（圖片）
- **文字格式**: Markdown (.md), 純文字 (.txt)
- **檔案大小**: 最大 16MB
- **上傳方式**: 拖拽上傳或點擊選擇

## 🛠️ 開發指南

### 新增元件
1. 在 `src/components/` 目錄下建立新元件
2. 在 `components.css` 中添加對應樣式
3. 在主應用程式中引入使用

### 修改樣式
- 全域樣式: 編輯 `src/index.css`
- 元件樣式: 編輯 `src/components/components.css`
- 使用 CSS 變數確保一致性

### API 擴展
- 在 `src/services/api.js` 中添加新的 API 函數
- 遵循現有的錯誤處理模式
- 添加適當的類型檢查

## 🧪 測試

應用程式包含內建的測試輔助函數：

- **檔案驗證測試**: 測試檔案格式和大小限制
- **API 連接測試**: 檢查後端服務連接狀態
- **元件渲染測試**: 驗證元件正確載入

在開發模式下，測試會自動執行並在控制台顯示結果。

## 🎨 自訂主題

應用程式使用 CSS 變數系統，可輕鬆自訂主題：

```css
:root {
  --primary-500: #0ea5e9;    /* 主色調 */
  --gray-50: #f9fafb;        /* 背景色 */
  --radius-md: 0.5rem;       /* 圓角大小 */
  --spacing-md: 1rem;        /* 間距 */
}
```


