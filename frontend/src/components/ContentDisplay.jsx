import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Calendar, User, Clock, Sparkles, Heart, Share2, Bookmark, Copy, Check } from 'lucide-react';

const ContentDisplay = ({ content, xiabianType, isLoading, error }) => {
  const [liked, setLiked] = useState(false);
  const [bookmarked, setBookmarked] = useState(false);
  const [copied, setCopied] = useState(false);
  const [readingTime, setReadingTime] = useState(0);

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('zh-TW', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getXiabianInfo = (type) => {
    return type === 'A'
      ? {
          name: '小編A',
          description: '專精於內容創作，具有獨特的寫作風格和專業知識',
          avatar: '✍️',
          color: '#6366f1',
          gradient: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)'
        }
      : {
          name: '小編JU',
          description: '專精於美妝保養內容創作，語言風格輕鬆親切、活潑熱情',
          avatar: '💄',
          color: '#ec4899',
          gradient: 'linear-gradient(135deg, #ec4899 0%, #f97316 100%)'
        };
  };

  const xiabianInfo = getXiabianInfo(xiabianType);

  // 計算閱讀時間
  useEffect(() => {
    if (content) {
      const wordsPerMinute = 200;
      const wordCount = content.split(/\s+/).length;
      const time = Math.ceil(wordCount / wordsPerMinute);
      setReadingTime(time);
    }
  }, [content]);

  const handleCopyContent = async () => {
    try {
      await navigator.clipboard.writeText(content);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('複製失敗:', err);
    }
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'AI 生成的精彩內容',
          text: content.substring(0, 100) + '...',
          url: window.location.href,
        });
      } catch (err) {
        console.error('分享失敗:', err);
      }
    } else {
      // 備用方案：複製到剪貼板
      handleCopyContent();
    }
  };

  return (
    <div className="content-display">
      <AnimatePresence mode="wait">
        {isLoading && (
          <motion.div
            key="loading"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="loading-state"
          >
            <div className="loading-spinner">
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              >
                <Sparkles size={32} />
              </motion.div>
            </div>
            <h3>AI 正在分析圖片並生成內容...</h3>
            <p>請稍候，{xiabianInfo.name} 正在為您創作精彩內容</p>
          </motion.div>
        )}

        {error && (
          <motion.div
            key="error"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="error-state"
          >
            <div className="error-icon">⚠️</div>
            <h3>內容生成失敗</h3>
            <p>{error}</p>
            <button className="retry-button">重試</button>
          </motion.div>
        )}

        {content && !isLoading && !error && (
          <motion.article
            key="content"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -30 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            className="blog-article"
          >
            {/* 文章標題區域 */}
            <motion.header 
              className="article-header"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              <div className="article-meta">
                <div className="author-info">
                  <div
                    className="author-avatar"
                    style={{ background: xiabianInfo.gradient }}
                  >
                    <span className="avatar-emoji">{xiabianInfo.avatar}</span>
                  </div>
                  <div className="author-details">
                    <h4>{xiabianInfo.name}</h4>
                    <p>{xiabianInfo.description}</p>
                  </div>
                </div>
                <div className="publish-info">
                  <div className="meta-item">
                    <Calendar size={16} />
                    <span>{formatDate(new Date())}</span>
                  </div>
                  <div className="meta-item">
                    <Clock size={16} />
                    <span>{readingTime} 分鐘閱讀</span>
                  </div>
                </div>
              </div>

              {/* 文章互動按鈕 */}
              <div className="article-actions">
                <motion.button
                  className={`action-btn ${liked ? 'liked' : ''}`}
                  onClick={() => setLiked(!liked)}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <Heart size={18} fill={liked ? '#ef4444' : 'none'} />
                  <span>{liked ? '已喜歡' : '喜歡'}</span>
                </motion.button>

                <motion.button
                  className={`action-btn ${bookmarked ? 'bookmarked' : ''}`}
                  onClick={() => setBookmarked(!bookmarked)}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <Bookmark size={18} fill={bookmarked ? '#3b82f6' : 'none'} />
                  <span>{bookmarked ? '已收藏' : '收藏'}</span>
                </motion.button>

                <motion.button
                  className="action-btn"
                  onClick={handleShare}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <Share2 size={18} />
                  <span>分享</span>
                </motion.button>

                <motion.button
                  className={`action-btn ${copied ? 'copied' : ''}`}
                  onClick={handleCopyContent}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  {copied ? <Check size={18} /> : <Copy size={18} />}
                  <span>{copied ? '已複製' : '複製'}</span>
                </motion.button>
              </div>
            </motion.header>

            {/* 文章內容 */}
            <motion.div 
              className="article-content"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.4 }}
            >
              <ReactMarkdown
                remarkPlugins={[remarkGfm]}
                components={{
                  h1: ({ children }) => (
                    <motion.h1
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.1 }}
                    >
                      {children}
                    </motion.h1>
                  ),
                  h2: ({ children }) => (
                    <motion.h2
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.2 }}
                    >
                      {children}
                    </motion.h2>
                  ),
                  h3: ({ children }) => (
                    <motion.h3
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.3 }}
                    >
                      {children}
                    </motion.h3>
                  ),
                  p: ({ children }) => (
                    <motion.p
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.1 }}
                    >
                      {children}
                    </motion.p>
                  ),
                  ul: ({ children }) => (
                    <motion.ul
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.2 }}
                    >
                      {children}
                    </motion.ul>
                  ),
                  ol: ({ children }) => (
                    <motion.ol
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.2 }}
                    >
                      {children}
                    </motion.ol>
                  ),
                  blockquote: ({ children }) => (
                    <motion.blockquote
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.3 }}
                      className="quote"
                    >
                      {children}
                    </motion.blockquote>
                  ),
                  code: ({ inline, children }) => (
                    inline ? (
                      <code className="inline-code">{children}</code>
                    ) : (
                      <motion.pre
                        initial={{ opacity: 0, scale: 0.95 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 0.4 }}
                        className="code-block"
                      >
                        <code>{children}</code>
                      </motion.pre>
                    )
                  )
                }}
              >
                {content}
              </ReactMarkdown>
            </motion.div>

            {/* 文章底部 */}
            <motion.footer 
              className="article-footer"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
            >
              <div className="tags">
                <span className="tag">AI生成</span>
                <span className="tag">{xiabianInfo.name}</span>
                <span className="tag">圖片分析</span>
              </div>
            </motion.footer>
          </motion.article>
        )}

        {!content && !isLoading && !error && (
          <motion.div
            key="empty"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="empty-state"
          >
            <div className="empty-icon">📝</div>
            <h3>準備好開始創作了嗎？</h3>
            <p>上傳一張圖片，讓 AI 小編為您生成精彩的內容</p>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default ContentDisplay;
