/* 現代化全域樣式 */
:root {
  /* 字體設定 */
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  line-height: 1.6;
  font-weight: 400;

  /* 顏色系統 */
  --primary-50: #f0f9ff;
  --primary-100: #e0f2fe;
  --primary-500: #0ea5e9;
  --primary-600: #0284c7;
  --primary-700: #0369a1;

  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  --success-500: #10b981;
  --error-500: #ef4444;
  --warning-500: #f59e0b;

  /* 陰影系統 */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* 邊框半徑 */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;

  /* 間距系統 */
  --spacing-xs: 0.5rem;
  --spacing-sm: 0.75rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;

  /* 動畫 */
  --transition-fast: 0.15s ease-out;
  --transition-normal: 0.25s ease-out;
  --transition-slow: 0.35s ease-out;

  color-scheme: light;
  color: var(--gray-800);
  background-color: var(--gray-50);

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 重置樣式 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background: linear-gradient(135deg, var(--gray-50) 0%, var(--primary-50) 100%);
}

/* 通用元素樣式 */
h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: 600;
  line-height: 1.3;
  color: var(--gray-900);
}

p {
  margin: 0;
  color: var(--gray-700);
}

button {
  border: none;
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: 0.875rem;
  font-weight: 500;
  font-family: inherit;
  cursor: pointer;
  transition: all var(--transition-normal);
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 主要按鈕樣式 */
.btn-primary {
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-primary:active {
  transform: translateY(0);
}

.btn-secondary {
  background: white;
  color: var(--gray-700);
  border: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--gray-50);
  border-color: var(--gray-300);
}

/* 輸入框樣式 */
input, textarea {
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  font-family: inherit;
  font-size: 0.875rem;
  transition: all var(--transition-normal);
}

input:focus, textarea:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgb(14 165 233 / 0.1);
}

/* 主應用程式佈局 */
.app-container {
  min-height: 100vh;
  padding: var(--spacing-xl);
}

.app-header {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
}

.app-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--spacing-sm);
}

.app-header p {
  font-size: 1.125rem;
  color: var(--gray-600);
  max-width: 600px;
  margin: 0 auto;
}

.main-content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: var(--spacing-2xl);
  max-width: 1400px;
  margin: 0 auto;
}

.left-panel {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.right-panel {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
}

/* 響應式設計 */
@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 1fr 1.5fr;
    gap: var(--spacing-xl);
  }
}

@media (max-width: 1024px) {
  .main-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
  }

  .app-container {
    padding: var(--spacing-lg);
  }

  .app-header h1 {
    font-size: 2rem;
  }

  /* 左側面板在平板上的調整 */
  .left-panel {
    order: 1;
  }

  .right-panel {
    order: 2;
    min-height: 500px;
  }
}

@media (max-width: 768px) {
  .app-container {
    padding: var(--spacing-md);
  }

  .app-header h1 {
    font-size: 1.75rem;
  }

  .main-content {
    gap: var(--spacing-lg);
  }

  /* 小編選擇器在手機上的調整 */
  .xiabian-options {
    gap: var(--spacing-md);
  }

  .xiabian-option {
    padding: var(--spacing-md);
  }

  .option-features {
    flex-wrap: wrap;
    gap: var(--spacing-xs);
  }

  .feature-tag {
    font-size: 0.75rem;
    padding: var(--spacing-xs);
  }

  /* 文章互動按鈕在手機上的調整 */
  .article-actions {
    flex-wrap: wrap;
    gap: var(--spacing-sm);
  }

  .action-btn {
    flex: 1;
    min-width: 0;
    justify-content: center;
  }

  /* Prompt 編輯器在手機上的調整 */
  .prompt-textarea {
    font-size: 0.875rem;
    min-height: 60px;
  }

  .prompt-actions {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .save-prompt-btn, .cancel-prompt-btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .app-header h1 {
    font-size: 1.5rem;
  }

  .app-header p {
    font-size: 1rem;
  }

  /* 檔案上傳區域在小螢幕上的調整 */
  .dropzone {
    padding: var(--spacing-lg);
  }

  .dropzone h3 {
    font-size: 1.125rem;
  }

  /* 文章內容在小螢幕上的調整 */
  .blog-article {
    padding: var(--spacing-lg);
  }

  .article-meta {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: flex-start;
  }

  .publish-info {
    align-items: flex-start;
  }

  .article-content h1 {
    font-size: 1.5rem;
  }

  .article-content h2 {
    font-size: 1.25rem;
  }

  .article-content h3 {
    font-size: 1.125rem;
  }
}
