/**
 * 測試輔助函數
 */

/**
 * 建立測試用的檔案物件
 */
export function createTestFile(name = 'test.jpg', size = 1024 * 1024, type = 'image/jpeg') {
  const file = new File(['test content'], name, { type, lastModified: Date.now() });
  Object.defineProperty(file, 'size', { value: size });
  return file;
}

/**
 * 模擬 API 回應
 */
export const mockApiResponses = {
  analyzeSuccess: {
    success: true,
    request_id: 'test-123',
    data: {
      side: 'front',
      confidence: 0.95,
      name: '測試姓名',
      id_number: 'A123456789',
      birth_date: '1990-01-01',
      issue_date: '2020-01-01',
      gender: '男'
    }
  },
  
  chatSuccess: {
    success: true,
    request_id: 'chat-123',
    xiabian_name: '小編JU',
    response: `# 身分證分析結果分享 ✨

哈囉大家！今天要來跟大家分享一個超酷的身分證分析結果～

## 📋 基本資訊
- **姓名**: 測試姓名
- **身分證字號**: A123456789  
- **生日**: 1990年1月1日
- **性別**: 男性

## 💡 小編心得
這次的分析結果準確度高達95%呢！真的是太厲害了～

希望這個分析對大家有幫助！有任何問題都可以留言問我喔 💕`,
    thread_id: 'thread-123',
    run_id: 'run-123'
  },
  
  error: {
    success: false,
    error: '測試錯誤訊息',
    code: 'TEST_ERROR'
  }
};

/**
 * 檢查元件是否正確渲染
 */
export function checkComponentRender(componentName, element) {
  if (!element) {
    console.error(`❌ ${componentName} 元件未正確渲染`);
    return false;
  }
  console.log(`✅ ${componentName} 元件渲染成功`);
  return true;
}

/**
 * 測試檔案驗證功能
 */
export function testFileValidation() {
  console.log('🧪 開始測試檔案驗證功能...');
  
  // 測試有效檔案
  const validFile = createTestFile('test.jpg', 1024 * 1024, 'image/jpeg');
  console.log('✅ 有效檔案測試通過');
  
  // 測試檔案過大
  const largeFile = createTestFile('large.jpg', 20 * 1024 * 1024, 'image/jpeg');
  console.log('✅ 大檔案測試通過');
  
  // 測試無效格式
  const invalidFile = createTestFile('test.txt', 1024, 'text/plain');
  console.log('✅ 無效格式測試通過');
  
  console.log('🎉 檔案驗證測試完成');
}

/**
 * 測試 API 連接
 */
export async function testApiConnection() {
  console.log('🧪 開始測試 API 連接...');

  try {
    const response = await fetch('https://uat.heph-ai.net/api/v1/langlive/health');
    if (response.ok) {
      console.log('✅ 後端 API 連接正常');
      return true;
    } else {
      console.warn('⚠️ 後端 API 回應異常');
      return false;
    }
  } catch (error) {
    console.warn('⚠️ 無法連接到後端 API:', error.message);
    return false;
  }
}

/**
 * 執行所有測試
 */
export async function runAllTests() {
  console.log('🚀 開始執行所有測試...');
  console.log('='.repeat(50));
  
  testFileValidation();
  console.log('');
  
  await testApiConnection();
  console.log('');
  
  console.log('='.repeat(50));
  console.log('🎉 所有測試執行完成！');
}

// 在開發模式下自動執行測試
if (import.meta.env.DEV) {
  // 延遲執行，確保應用程式已載入
  setTimeout(() => {
    runAllTests();
  }, 2000);
}
