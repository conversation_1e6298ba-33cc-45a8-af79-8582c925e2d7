#!/usr/bin/env python3
"""
身分證件資料分析器
功能：使用 Google Gemini API 進行身分證 OCR 文字辨識和資料提取
"""

import os
import json
import base64
import re
from datetime import datetime
from pathlib import Path
from typing import Dict, Optional, Any, List
import logging
import google.generativeai as genai
from PIL import Image
import io
import requests
import boto3

# 設定日誌
os.makedirs('logs', exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/id_analyzer.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class IDCardAnalyzer:
    """身分證件分析器"""
    
    def __init__(self, output_dir: str = "id_analysis_results"):
        """
        初始化分析器
        
        Args:
            output_dir: 分析結果輸出目錄
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # API 設定
        self.gemini_api_key = os.getenv('GEMINI_API_KEY')
        self.openai_api_key = os.getenv('OPENAI_API_KEY')
        self.aws_access_key = os.getenv('AWS_ACCESS_KEY_ID')
        self.aws_secret_key = os.getenv('AWS_SECRET_ACCESS_KEY')
        self.aws_region = os.getenv('AWS_DEFAULT_REGION', 'us-east-1')

        # 檢查 API 金鑰
        if not any([self.gemini_api_key, self.openai_api_key, (self.aws_access_key and self.aws_secret_key)]):
            logger.error("未設定任何 API 金鑰")
            raise ValueError("請在 .env 檔案中設定至少一個 API 金鑰")

        # 初始化 Gemini
        if self.gemini_api_key:
            genai.configure(api_key=self.gemini_api_key)
            self.gemini_model = genai.GenerativeModel('gemini-1.5-flash')
            logger.info("Gemini API 初始化完成")
        else:
            self.gemini_model = None
            logger.warning("未設定 GEMINI_API_KEY，Gemini 功能將不可用")

        # OpenAI 設定
        if self.openai_api_key:
            logger.info("OpenAI API 金鑰已設定")
        else:
            logger.warning("未設定 OPENAI_API_KEY，OpenAI 功能將不可用")

        # AWS Bedrock Claude 設定
        if self.aws_access_key and self.aws_secret_key:
            try:
                self.bedrock_client = boto3.client(
                    'bedrock-runtime',
                    aws_access_key_id=self.aws_access_key,
                    aws_secret_access_key=self.aws_secret_key,
                    region_name=self.aws_region
                )
                logger.info("AWS Bedrock Claude 初始化完成")
            except Exception as e:
                logger.error(f"AWS Bedrock 初始化失敗: {e}")
                self.bedrock_client = None
        else:
            self.bedrock_client = None
            logger.warning("未設定 AWS 憑證，Claude 功能將不可用")

        logger.info("身分證分析器初始化完成")
    
    def validate_image(self, image_path: str) -> bool:
        """
        驗證圖片檔案
        
        Args:
            image_path: 圖片檔案路徑
            
        Returns:
            是否為有效圖片
        """
        try:
            if not os.path.exists(image_path):
                logger.error(f"圖片檔案不存在: {image_path}")
                return False
            
            # 檢查檔案大小（限制 10MB）
            file_size = os.path.getsize(image_path)
            if file_size > 10 * 1024 * 1024:
                logger.error(f"圖片檔案過大: {file_size / 1024 / 1024:.2f}MB")
                return False
            
            # 嘗試開啟圖片
            with Image.open(image_path) as img:
                # 檢查圖片格式
                if img.format not in ['JPEG', 'PNG', 'WEBP']:
                    logger.error(f"不支援的圖片格式: {img.format}")
                    return False
                
                # 檢查圖片尺寸
                width, height = img.size
                if width < 100 or height < 100:
                    logger.error(f"圖片尺寸過小: {width}x{height}")
                    return False
                
                logger.info(f"圖片驗證通過: {width}x{height}, {img.format}, {file_size/1024:.1f}KB")
                return True
                
        except Exception as e:
            logger.error(f"圖片驗證失敗: {e}")
            return False
    
    def preprocess_image(self, image_path: str) -> Optional[Image.Image]:
        """
        預處理圖片
        
        Args:
            image_path: 圖片檔案路徑
            
        Returns:
            處理後的圖片物件
        """
        try:
            with Image.open(image_path) as img:
                # 轉換為 RGB 格式
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # 調整圖片大小（如果太大）
                max_size = 2048
                if max(img.size) > max_size:
                    ratio = max_size / max(img.size)
                    new_size = (int(img.width * ratio), int(img.height * ratio))
                    img = img.resize(new_size, Image.Resampling.LANCZOS)
                    logger.info(f"圖片已調整大小: {new_size}")
                
                return img.copy()
                
        except Exception as e:
            logger.error(f"圖片預處理失敗: {e}")
            return None

    def _detect_id_side(self, img: Image.Image) -> str:
        """
        檢測身分證是正面還是背面

        Args:
            img: 圖片物件

        Returns:
            "front" 或 "back"
        """
        try:
            if not self.gemini_model:
                logger.warning("無法使用 Gemini 進行面別檢測，預設為正面")
                return "front"

            # 使用 Gemini 檢測面別
            detection_prompt = """請判斷這張圖片是台灣身分證的正面還是背面。

正面特徵：
- 有個人照片
- 有姓名、身分證號碼
- 有出生年月日、發證日期
- 有性別資訊

背面特徵：
- 沒有個人照片
- 有父親、母親姓名
- 有配偶、役別資訊
- 有出生地、住址資訊

請只回答 "front" 或 "back"，不要包含其他文字。"""

            response = self.gemini_model.generate_content([detection_prompt, img])

            if response.text:
                detected = response.text.strip().lower()
                if "back" in detected:
                    logger.info("檢測到身分證面別: back")
                    return "back"
                else:
                    logger.info("檢測到身分證面別: front")
                    return "front"
            else:
                logger.warning("面別檢測無回應，預設為正面")
                return "front"

        except Exception as e:
            logger.error(f"面別檢測失敗: {e}")
            return "front"

    def _get_front_side_prompt(self) -> str:
        """取得正面身分證的分析提示"""
        return """請仔細分析這張台灣身分證正面圖片，並提取以下資訊：

1. 姓名 (name)
2. 身分證號碼 (id_number)
3. 出生年月日 (birth_date) - 格式：YYYY/MM/DD
4. 發證日期 (issue_date) - 格式：YYYY/MM/DD
5. 性別 (gender) - 男 或 女

請以 JSON 格式回傳結果，格式如下：
{
    "name": "提取的姓名",
    "id_number": "提取的身分證號碼",
    "birth_date": "出生年月日",
    "issue_date": "發證日期",
    "gender": "性別",
    "confidence": "整體辨識信心度 (0-1之間的數值)",
    "details": {
        "name_confidence": "姓名辨識信心度",
        "id_confidence": "身分證號碼辨識信心度",
        "birth_date_confidence": "出生日期辨識信心度",
        "issue_date_confidence": "發證日期辨識信心度",
        "gender_confidence": "性別辨識信心度"
    }
}

注意事項：
- 如果某個欄位無法辨識，請填入 null
- 信心度請根據文字清晰度和辨識準確度評估
- 身分證號碼格式應為：1個英文字母 + 9個數字
- 日期格式請統一為 YYYY/MM/DD
- 性別請填入「男」或「女」
- 請確保 JSON 格式正確"""

    def _get_back_side_prompt(self) -> str:
        """取得背面身分證的分析提示"""
        return """請仔細分析這張台灣身分證背面圖片，並提取以下資訊：

1. 父親姓名 (father)
2. 母親姓名 (mother)
3. 配偶姓名 (spouse)
4. 役別 (military_service)
5. 出生地 (birth_place)
6. 住址 (address)

請以 JSON 格式回傳結果，格式如下：
{
    "father": "父親姓名",
    "mother": "母親姓名",
    "spouse": "配偶姓名",
    "military_service": "役別",
    "birth_place": "出生地",
    "address": "完整住址",
    "confidence": "整體辨識信心度 (0-1之間的數值)",
    "details": {
        "father_confidence": "父親姓名辨識信心度",
        "mother_confidence": "母親姓名辨識信心度",
        "spouse_confidence": "配偶姓名辨識信心度",
        "military_service_confidence": "役別辨識信心度",
        "birth_place_confidence": "出生地辨識信心度",
        "address_confidence": "住址辨識信心度"
    }
}

注意事項：
- 如果某個欄位無法辨識或為空白，請填入 null
- 信心度請根據文字清晰度和辨識準確度評估
- 住址請提取完整地址資訊
- 配偶欄位如果是空白或未婚，請填入 null
- 請確保 JSON 格式正確"""

    def _get_general_prompt(self) -> str:
        """取得通用的分析提示（當無法確定正反面時）"""
        return """請仔細分析這張台灣身分證圖片，並提取所有可見的資訊：

可能的欄位包括：
- 姓名 (name)
- 身分證號碼 (id_number)
- 出生年月日 (birth_date)
- 發證日期 (issue_date)
- 性別 (gender)
- 父親姓名 (father)
- 母親姓名 (mother)
- 配偶姓名 (spouse)
- 役別 (military_service)
- 出生地 (birth_place)
- 住址 (address)

請以 JSON 格式回傳結果，只包含能夠辨識的欄位：
{
    "欄位名": "提取的值",
    "confidence": "整體辨識信心度 (0-1之間的數值)"
}

注意事項：
- 只回傳能夠清楚辨識的欄位
- 如果某個欄位無法辨識，請不要包含在結果中
- 信心度請根據文字清晰度和辨識準確度評估
- 請確保 JSON 格式正確"""
    
    def extract_id_info(self, image_path: str, side: str = "auto") -> Dict[str, Any]:
        """
        從身分證圖片中提取資訊

        Args:
            image_path: 身分證圖片路徑
            side: 身分證面別 ("front", "back", "auto")

        Returns:
            提取的身分證資訊
        """
        logger.info(f"開始分析身分證: {image_path} (面別: {side})")

        if not self.gemini_model:
            return self._create_error_result("未設定 Gemini API 金鑰")

        # 驗證圖片
        if not self.validate_image(image_path):
            return self._create_error_result("圖片驗證失敗")

        # 預處理圖片
        processed_img = self.preprocess_image(image_path)
        if not processed_img:
            return self._create_error_result("圖片預處理失敗")

        try:
            # 根據面別建立不同的分析提示
            if side == "front":
                prompt = self._get_front_side_prompt()
                expected_fields = ["name", "id_number", "birth_date", "issue_date", "gender"]
            elif side == "back":
                prompt = self._get_back_side_prompt()
                expected_fields = ["father", "mother", "spouse", "military_service", "birth_place", "address"]
            else:  # auto
                # 自動檢測面別
                detected_side = self._detect_id_side(processed_img)
                if detected_side == "front":
                    prompt = self._get_front_side_prompt()
                    expected_fields = ["name", "id_number", "birth_date", "issue_date", "gender"]
                else:
                    prompt = self._get_back_side_prompt()
                    expected_fields = ["father", "mother", "spouse", "military_service", "birth_place", "address"]
                side = detected_side

            # 呼叫 Gemini API
            response = self.gemini_model.generate_content([prompt, processed_img])

            if not response.text:
                return self._create_error_result("Gemini API 未返回結果")

            logger.info("Gemini API 分析完成")

            # 解析回應
            result = self._parse_gemini_response(response.text)

            # 驗證提取的資料
            validated_result = self._validate_extracted_data(result, expected_fields)

            # 加入處理時間戳和面別資訊
            validated_result['timestamp'] = datetime.now().isoformat()
            validated_result['image_path'] = image_path
            validated_result['source'] = 'gemini'
            validated_result['detected_side'] = side

            logger.info(f"身分證分析完成: {validated_result.get('name', 'Unknown')}")
            return validated_result

        except Exception as e:
            logger.error(f"身分證分析失敗: {e}")
            return self._create_error_result(f"分析過程發生錯誤: {str(e)}")

    def extract_id_info_with_key(self, image_path: str, api_key: str, side: str = "auto") -> Dict[str, Any]:
        """
        使用客戶提供的 API key 從身分證圖片中提取資訊

        Args:
            image_path: 身分證圖片路徑
            api_key: 客戶提供的 Gemini API key
            side: 身分證面別 ("front", "back", "auto")

        Returns:
            提取的身分證資訊
        """
        if not api_key:
            return self._create_error_result("未提供 Gemini API key")

        logger.info(f"使用客戶 API key 開始分析身分證: {image_path} (面別: {side})")

        # 儲存原始設定
        original_api_key = self.gemini_api_key
        original_model = self.gemini_model

        try:
            # 使用客戶提供的 API key 建立臨時模型
            genai.configure(api_key=api_key)
            temp_model = genai.GenerativeModel('gemini-1.5-flash')

            # 驗證圖片
            if not self.validate_image(image_path):
                return self._create_error_result("圖片驗證失敗")

            # 預處理圖片
            processed_img = self.preprocess_image(image_path)
            if not processed_img:
                return self._create_error_result("圖片預處理失敗")

            # 檢測面別
            if side == "auto":
                side = self.detect_side(processed_img)
                logger.info(f"自動檢測面別: {side}")

            # 根據面別設定預期欄位
            expected_fields = self.FRONT_FIELDS if side == "front" else self.BACK_FIELDS

            # 取得分析提示
            prompt = self._get_analysis_prompt(side)

            # 呼叫 Gemini API
            response = temp_model.generate_content([prompt, processed_img])

            if not response.text:
                return self._create_error_result("Gemini API 未返回結果")

            logger.info("使用客戶 API key 的 Gemini API 分析完成")

            # 解析回應
            result = self._parse_gemini_response(response.text)

            # 驗證提取的資料
            validated_result = self._validate_extracted_data(result, expected_fields)

            # 加入處理時間戳和面別資訊
            validated_result['timestamp'] = datetime.now().isoformat()
            validated_result['image_path'] = image_path
            validated_result['source'] = 'gemini_custom_key'
            validated_result['detected_side'] = side

            logger.info(f"使用客戶 API key 身分證分析完成: {validated_result.get('name', 'Unknown')}")
            return validated_result

        except Exception as e:
            logger.error(f"使用客戶 API key 分析失敗: {e}")
            return self._create_error_result(f"API key 無效或分析失敗: {str(e)}")

        finally:
            # 恢復原始設定
            if original_api_key:
                genai.configure(api_key=original_api_key)
                self.gemini_model = original_model
            else:
                # 如果原本沒有設定，則清除設定
                self.gemini_model = None

    def extract_id_info_openai(self, image_path: str, side: str = "auto") -> Dict[str, Any]:
        """
        使用 OpenAI 從身分證圖片中提取資訊

        Args:
            image_path: 身分證圖片路徑

        Returns:
            提取的身分證資訊
        """
        logger.info(f"開始使用 OpenAI 分析身分證: {image_path} (面別: {side})")

        if not self.openai_api_key:
            return self._create_error_result("未設定 OPENAI_API_KEY")

        # 驗證圖片
        if not self.validate_image(image_path):
            return self._create_error_result("圖片驗證失敗")

        # 預處理圖片
        processed_img = self.preprocess_image(image_path)
        if not processed_img:
            return self._create_error_result("圖片預處理失敗")

        try:
            # 將圖片轉換為 base64
            import io
            img_buffer = io.BytesIO()
            processed_img.save(img_buffer, format='JPEG')
            img_base64 = base64.b64encode(img_buffer.getvalue()).decode('utf-8')

            # 根據面別建立不同的分析提示
            if side == "front":
                prompt = self._get_front_side_prompt()
                expected_fields = ["name", "id_number", "birth_date", "issue_date", "gender"]
            elif side == "back":
                prompt = self._get_back_side_prompt()
                expected_fields = ["father", "mother", "spouse", "military_service", "birth_place", "address"]
            else:  # auto
                # 自動檢測面別
                detected_side = self._detect_id_side(processed_img)
                if detected_side == "front":
                    prompt = self._get_front_side_prompt()
                    expected_fields = ["name", "id_number", "birth_date", "issue_date", "gender"]
                else:
                    prompt = self._get_back_side_prompt()
                    expected_fields = ["father", "mother", "spouse", "military_service", "birth_place", "address"]
                side = detected_side

            # 呼叫 OpenAI API
            headers = {
                'Authorization': f'Bearer {self.openai_api_key}',
                'Content-Type': 'application/json'
            }

            data = {
                'model': 'gpt-4o',
                'messages': [
                    {
                        'role': 'user',
                        'content': [
                            {
                                'type': 'text',
                                'text': prompt
                            },
                            {
                                'type': 'image_url',
                                'image_url': {
                                    'url': f'data:image/jpeg;base64,{img_base64}'
                                }
                            }
                        ]
                    }
                ],
                'max_tokens': 1000,
                'temperature': 0.1
            }

            response = requests.post(
                'https://api.openai.com/v1/chat/completions',
                headers=headers,
                json=data,
                timeout=60
            )

            if response.status_code != 200:
                logger.error(f"OpenAI API 錯誤: {response.status_code} - {response.text}")
                return self._create_error_result(f"OpenAI API 錯誤: {response.status_code}")

            result_data = response.json()
            response_text = result_data['choices'][0]['message']['content']

            logger.info("OpenAI API 分析完成")

            # 解析回應
            result = self._parse_openai_response(response_text)

            # 驗證提取的資料
            validated_result = self._validate_extracted_data(result, expected_fields)

            # 加入處理時間戳和來源
            validated_result['timestamp'] = datetime.now().isoformat()
            validated_result['image_path'] = image_path
            validated_result['source'] = 'openai'
            validated_result['detected_side'] = side

            logger.info(f"OpenAI 身分證分析完成: {validated_result.get('name', 'Unknown')}")
            return validated_result

        except Exception as e:
            logger.error(f"OpenAI 身分證分析失敗: {e}")
            return self._create_error_result(f"OpenAI 分析過程發生錯誤: {str(e)}")

    def _parse_openai_response(self, response_text: str) -> Dict[str, Any]:
        """
        解析 OpenAI API 回應

        Args:
            response_text: API 回應文字

        Returns:
            解析後的資料字典
        """
        try:
            # 移除可能的 markdown 格式標記
            cleaned_text = response_text.strip()
            if cleaned_text.startswith('```json'):
                cleaned_text = cleaned_text[7:]
            if cleaned_text.endswith('```'):
                cleaned_text = cleaned_text[:-3]
            cleaned_text = cleaned_text.strip()

            # 解析 JSON
            result = json.loads(cleaned_text)
            logger.info("成功解析 OpenAI 回應")
            return result

        except json.JSONDecodeError as e:
            logger.error(f"OpenAI JSON 解析失敗: {e}")
            logger.error(f"原始回應: {response_text}")

            # 嘗試從文字中提取資訊
            return self._extract_from_text(response_text)

    def extract_id_info_claude(self, image_path: str, side: str = "auto") -> Dict[str, Any]:
        """
        使用 AWS Bedrock Claude 從身分證圖片中提取資訊

        Args:
            image_path: 身分證圖片路徑

        Returns:
            提取的身分證資訊
        """
        logger.info(f"開始使用 Claude 分析身分證: {image_path} (面別: {side})")

        if not self.bedrock_client:
            return self._create_error_result("未設定 AWS Bedrock 憑證")

        # 驗證圖片
        if not self.validate_image(image_path):
            return self._create_error_result("圖片驗證失敗")

        # 預處理圖片
        processed_img = self.preprocess_image(image_path)
        if not processed_img:
            return self._create_error_result("圖片預處理失敗")

        try:
            # 將圖片轉換為 base64
            import io
            img_buffer = io.BytesIO()
            processed_img.save(img_buffer, format='JPEG')
            img_base64 = base64.b64encode(img_buffer.getvalue()).decode('utf-8')

            # 根據面別建立不同的分析提示
            if side == "front":
                prompt = self._get_front_side_prompt()
                expected_fields = ["name", "id_number", "birth_date", "issue_date", "gender"]
            elif side == "back":
                prompt = self._get_back_side_prompt()
                expected_fields = ["father", "mother", "spouse", "military_service", "birth_place", "address"]
            else:  # auto
                # 自動檢測面別
                detected_side = self._detect_id_side(processed_img)
                if detected_side == "front":
                    prompt = self._get_front_side_prompt()
                    expected_fields = ["name", "id_number", "birth_date", "issue_date", "gender"]
                else:
                    prompt = self._get_back_side_prompt()
                    expected_fields = ["father", "mother", "spouse", "military_service", "birth_place", "address"]
                side = detected_side

            # 準備 Claude 請求
            message = {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": prompt
                    },
                    {
                        "type": "image",
                        "source": {
                            "type": "base64",
                            "media_type": "image/jpeg",
                            "data": img_base64
                        }
                    }
                ]
            }

            # 呼叫 Claude API
            response = self.bedrock_client.invoke_model(
                modelId='anthropic.claude-3-sonnet-20240229-v1:0',
                body=json.dumps({
                    "anthropic_version": "bedrock-2023-05-31",
                    "max_tokens": 1000,
                    "temperature": 0.1,
                    "messages": [message]
                })
            )

            # 解析回應
            response_body = json.loads(response['body'].read())
            response_text = response_body['content'][0]['text']

            logger.info("Claude API 分析完成")

            # 解析回應
            result = self._parse_claude_response(response_text)

            # 驗證提取的資料
            validated_result = self._validate_extracted_data(result, expected_fields)

            # 加入處理時間戳和來源
            validated_result['timestamp'] = datetime.now().isoformat()
            validated_result['image_path'] = image_path
            validated_result['source'] = 'claude'
            validated_result['detected_side'] = side

            logger.info(f"Claude 身分證分析完成: {validated_result.get('name', 'Unknown')}")
            return validated_result

        except Exception as e:
            logger.error(f"Claude 身分證分析失敗: {e}")
            return self._create_error_result(f"Claude 分析過程發生錯誤: {str(e)}")

    def _parse_claude_response(self, response_text: str) -> Dict[str, Any]:
        """
        解析 Claude API 回應

        Args:
            response_text: API 回應文字

        Returns:
            解析後的資料字典
        """
        try:
            # 移除可能的 markdown 格式標記
            cleaned_text = response_text.strip()
            if cleaned_text.startswith('```json'):
                cleaned_text = cleaned_text[7:]
            if cleaned_text.endswith('```'):
                cleaned_text = cleaned_text[:-3]
            cleaned_text = cleaned_text.strip()

            # 解析 JSON
            result = json.loads(cleaned_text)
            logger.info("成功解析 Claude 回應")
            return result

        except json.JSONDecodeError as e:
            logger.error(f"Claude JSON 解析失敗: {e}")
            logger.error(f"原始回應: {response_text}")

            # 嘗試從文字中提取資訊
            return self._extract_from_text(response_text)

    def compare_analysis_results(self, image_path: str, side: str = "auto") -> Dict[str, Any]:
        """
        同時使用 Gemini、OpenAI 和 Claude 分析身分證並比較結果

        Args:
            image_path: 身分證圖片路徑

        Returns:
            包含三個 API 結果和比較分析的字典
        """
        logger.info(f"開始三方比較分析: {image_path}")

        results = {
            'image_path': image_path,
            'timestamp': datetime.now().isoformat(),
            'gemini_result': None,
            'openai_result': None,
            'claude_result': None,
            'comparison': None
        }

        # 確定要使用的面別
        if side != "auto":
            detected_side = side
            logger.info(f"使用指定面別: {detected_side}")
        else:
            # 先用 Gemini 檢測面別
            if self.gemini_model:
                temp_result = self.extract_id_info(image_path, "auto")
                detected_side = temp_result.get('detected_side', 'front')
                logger.info(f"檢測到身分證面別: {detected_side}")
            else:
                detected_side = "front"
                logger.info("無法檢測面別，預設為正面")

        # 執行 Gemini 分析
        if self.gemini_model:
            logger.info("執行 Gemini 分析...")
            gemini_result = self.extract_id_info(image_path, detected_side)
            gemini_result['source'] = 'gemini'
            results['gemini_result'] = gemini_result
        else:
            logger.warning("Gemini 不可用，跳過 Gemini 分析")

        # 執行 OpenAI 分析
        if self.openai_api_key:
            logger.info("執行 OpenAI 分析...")
            openai_result = self.extract_id_info_openai(image_path, detected_side)
            results['openai_result'] = openai_result
        else:
            logger.warning("OpenAI 不可用，跳過 OpenAI 分析")

        # 執行 Claude 分析
        if self.bedrock_client:
            logger.info("執行 Claude 分析...")
            claude_result = self.extract_id_info_claude(image_path, detected_side)
            results['claude_result'] = claude_result
        else:
            logger.warning("Claude 不可用，跳過 Claude 分析")

        # 比較結果
        available_results = [r for r in [results['gemini_result'], results['openai_result'], results['claude_result']] if r and not r.get('error')]
        if len(available_results) >= 2:
            comparison = self._compare_multiple_results(available_results)
            results['comparison'] = comparison

        logger.info("三方比較分析完成")
        return results

    def _compare_results(self, gemini_result: Dict[str, Any], openai_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        比較兩個分析結果

        Args:
            gemini_result: Gemini 分析結果
            openai_result: OpenAI 分析結果

        Returns:
            比較分析結果
        """
        comparison = {
            'fields_comparison': {},
            'confidence_comparison': {},
            'agreement': {},
            'recommendation': {}
        }

        # 根據檢測到的面別確定要比較的欄位
        detected_side = results[0].get('detected_side', 'front')
        if detected_side == 'front':
            fields = ['name', 'id_number', 'birth_date', 'issue_date', 'gender']
        else:
            fields = ['father', 'mother', 'spouse', 'military_service', 'birth_place', 'address']

        for field in fields:
            gemini_value = gemini_result.get(field)
            openai_value = openai_result.get(field)

            # 欄位比較
            comparison['fields_comparison'][field] = {
                'gemini': gemini_value,
                'openai': openai_value,
                'match': gemini_value == openai_value if gemini_value and openai_value else False,
                'both_detected': bool(gemini_value and openai_value),
                'similarity': self._calculate_similarity(gemini_value, openai_value) if gemini_value and openai_value else 0
            }

        # 比較信心度
        gemini_confidence = gemini_result.get('confidence', 0)
        openai_confidence = openai_result.get('confidence', 0)

        comparison['confidence_comparison'] = {
            'gemini': gemini_confidence,
            'openai': openai_confidence,
            'difference': abs(gemini_confidence - openai_confidence),
            'higher_confidence': 'gemini' if gemini_confidence > openai_confidence else 'openai'
        }

        # 整體一致性分析
        total_matches = sum(1 for field in fields if comparison['fields_comparison'][field]['match'])
        total_fields = len(fields)

        comparison['agreement'] = {
            'exact_matches': total_matches,
            'total_fields': total_fields,
            'agreement_rate': total_matches / total_fields,
            'overall_similarity': sum(comparison['fields_comparison'][field]['similarity'] for field in fields) / total_fields
        }

        # 推薦結果
        if comparison['agreement']['agreement_rate'] >= 0.8:
            comparison['recommendation'] = {
                'status': 'high_agreement',
                'message': '兩個 API 結果高度一致，可信度高',
                'suggested_source': 'either'
            }
        elif comparison['confidence_comparison']['difference'] > 0.2:
            higher_conf_source = comparison['confidence_comparison']['higher_confidence']
            comparison['recommendation'] = {
                'status': 'confidence_difference',
                'message': f'{higher_conf_source.upper()} 的信心度明顯較高',
                'suggested_source': higher_conf_source
            }
        else:
            comparison['recommendation'] = {
                'status': 'manual_review',
                'message': '結果存在差異，建議人工檢查',
                'suggested_source': 'manual_review'
            }

        return comparison

    def _compare_multiple_results(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        比較多個分析結果

        Args:
            results: 多個分析結果的列表

        Returns:
            多方比較分析結果
        """
        comparison = {
            'sources': [r.get('source', 'unknown') for r in results],
            'fields_comparison': {},
            'confidence_comparison': {},
            'consensus': {},
            'recommendation': {}
        }

        fields = ['name', 'id_number', 'address']

        # 比較各個欄位
        for field in fields:
            field_values = [(r.get(field), r.get('source', 'unknown')) for r in results]
            field_values = [(v, s) for v, s in field_values if v]  # 過濾空值

            # 計算一致性
            unique_values = list(set([v for v, s in field_values]))

            comparison['fields_comparison'][field] = {
                'values': {source: value for value, source in field_values},
                'unique_count': len(unique_values),
                'consensus_value': unique_values[0] if len(unique_values) == 1 else None,
                'agreement_rate': 1.0 if len(unique_values) <= 1 else 0.0,
                'similarities': {}
            }

            # 計算相似度矩陣
            if len(field_values) > 1:
                for i, (v1, s1) in enumerate(field_values):
                    for j, (v2, s2) in enumerate(field_values):
                        if i < j:
                            similarity = self._calculate_similarity(v1, v2)
                            comparison['fields_comparison'][field]['similarities'][f"{s1}_vs_{s2}"] = similarity

        # 比較信心度
        confidences = [(r.get('confidence', 0), r.get('source', 'unknown')) for r in results]
        comparison['confidence_comparison'] = {
            'values': {source: conf for conf, source in confidences},
            'highest': max(confidences, key=lambda x: x[0]) if confidences else (0, 'none'),
            'average': sum([c for c, s in confidences]) / len(confidences) if confidences else 0,
            'variance': self._calculate_variance([c for c, s in confidences])
        }

        # 計算整體一致性
        total_consensus = sum(1 for field in fields if comparison['fields_comparison'][field]['consensus_value'] is not None)
        overall_agreement = total_consensus / len(fields)

        comparison['consensus'] = {
            'fields_with_consensus': total_consensus,
            'total_fields': len(fields),
            'overall_agreement_rate': overall_agreement,
            'average_similarity': self._calculate_average_similarity(comparison['fields_comparison'])
        }

        # 推薦結果
        if overall_agreement >= 0.8:
            comparison['recommendation'] = {
                'status': 'high_consensus',
                'message': '多個 API 結果高度一致，可信度很高',
                'suggested_action': 'accept_consensus'
            }
        elif comparison['confidence_comparison']['variance'] > 0.1:
            highest_conf_source = comparison['confidence_comparison']['highest'][1]
            comparison['recommendation'] = {
                'status': 'confidence_based',
                'message': f'{highest_conf_source.upper()} 的信心度最高，建議採用',
                'suggested_action': f'use_{highest_conf_source}'
            }
        else:
            comparison['recommendation'] = {
                'status': 'manual_review',
                'message': '結果存在分歧，建議人工檢查或採用多數決',
                'suggested_action': 'manual_review'
            }

        return comparison

    def _calculate_variance(self, values: List[float]) -> float:
        """計算變異數"""
        if len(values) < 2:
            return 0.0

        mean = sum(values) / len(values)
        variance = sum((x - mean) ** 2 for x in values) / len(values)
        return variance

    def _calculate_average_similarity(self, fields_comparison: Dict) -> float:
        """計算平均相似度"""
        all_similarities = []
        for field_data in fields_comparison.values():
            similarities = field_data.get('similarities', {})
            all_similarities.extend(similarities.values())

        return sum(all_similarities) / len(all_similarities) if all_similarities else 0.0

    def _calculate_similarity(self, str1: str, str2: str) -> float:
        """
        計算兩個字串的相似度

        Args:
            str1: 第一個字串
            str2: 第二個字串

        Returns:
            相似度 (0-1)
        """
        if not str1 or not str2:
            return 0.0

        # 簡單的字元相似度計算
        str1 = str1.lower().strip()
        str2 = str2.lower().strip()

        if str1 == str2:
            return 1.0

        # 計算編輯距離
        def levenshtein_distance(s1, s2):
            if len(s1) < len(s2):
                return levenshtein_distance(s2, s1)

            if len(s2) == 0:
                return len(s1)

            previous_row = list(range(len(s2) + 1))
            for i, c1 in enumerate(s1):
                current_row = [i + 1]
                for j, c2 in enumerate(s2):
                    insertions = previous_row[j + 1] + 1
                    deletions = current_row[j] + 1
                    substitutions = previous_row[j] + (c1 != c2)
                    current_row.append(min(insertions, deletions, substitutions))
                previous_row = current_row

            return previous_row[-1]

        max_len = max(len(str1), len(str2))
        distance = levenshtein_distance(str1, str2)
        similarity = 1 - (distance / max_len)

        return max(0.0, similarity)
    
    def _parse_gemini_response(self, response_text: str) -> Dict[str, Any]:
        """
        解析 Gemini API 回應
        
        Args:
            response_text: API 回應文字
            
        Returns:
            解析後的資料字典
        """
        try:
            # 移除可能的 markdown 格式標記
            cleaned_text = response_text.strip()
            if cleaned_text.startswith('```json'):
                cleaned_text = cleaned_text[7:]
            if cleaned_text.endswith('```'):
                cleaned_text = cleaned_text[:-3]
            cleaned_text = cleaned_text.strip()
            
            # 解析 JSON
            result = json.loads(cleaned_text)
            logger.info("成功解析 Gemini 回應")
            return result
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON 解析失敗: {e}")
            logger.error(f"原始回應: {response_text}")
            
            # 嘗試從文字中提取資訊
            return self._extract_from_text(response_text)
    
    def _extract_from_text(self, text: str) -> Dict[str, Any]:
        """
        從純文字中提取身分證資訊（備用方法）
        
        Args:
            text: 文字內容
            
        Returns:
            提取的資訊字典
        """
        result = {
            "name": None,
            "id_number": None,
            "address": None,
            "confidence": 0.5,
            "details": {
                "name_confidence": 0.5,
                "id_confidence": 0.5,
                "address_confidence": 0.5
            }
        }
        
        # 使用正則表達式提取身分證號碼
        id_pattern = r'[A-Z]\d{9}'
        id_match = re.search(id_pattern, text)
        if id_match:
            result["id_number"] = id_match.group()
            result["details"]["id_confidence"] = 0.8
        
        logger.warning("使用備用文字提取方法")
        return result
    
    def _validate_extracted_data(self, data: Dict[str, Any], expected_fields: List[str] = None) -> Dict[str, Any]:
        """
        驗證提取的資料

        Args:
            data: 提取的資料
            expected_fields: 預期的欄位列表

        Returns:
            驗證後的資料
        """
        validated = data.copy()

        # 驗證身分證號碼格式
        if data.get('id_number'):
            id_pattern = r'^[A-Z]\d{9}$'
            if not re.match(id_pattern, data['id_number']):
                logger.warning(f"身分證號碼格式不正確: {data['id_number']}")
                validated['id_number'] = None
                if 'details' in validated:
                    validated['details']['id_confidence'] = 0.1

        # 驗證日期格式
        date_fields = ['birth_date', 'issue_date']
        for field in date_fields:
            if data.get(field):
                # 嘗試解析日期格式
                date_str = data[field].strip()
                # 支援多種日期格式
                date_patterns = [
                    r'^\d{4}/\d{2}/\d{2}$',  # YYYY/MM/DD
                    r'^\d{3}/\d{2}/\d{2}$',  # YYY/MM/DD (民國年)
                    r'^\d{2}/\d{2}/\d{2}$'   # YY/MM/DD
                ]

                valid_date = False
                for pattern in date_patterns:
                    if re.match(pattern, date_str):
                        valid_date = True
                        break

                if not valid_date:
                    logger.warning(f"{field} 日期格式不正確: {date_str}")
                    validated[field] = None
                    if 'details' in validated:
                        validated['details'][f'{field}_confidence'] = 0.1

        # 驗證性別
        if data.get('gender'):
            gender = data['gender'].strip()
            if gender not in ['男', '女', 'M', 'F']:
                logger.warning(f"性別格式不正確: {gender}")
                validated['gender'] = None
                if 'details' in validated:
                    validated['details']['gender_confidence'] = 0.1

        # 清理文字欄位（移除多餘空白）
        text_fields = ['name', 'address', 'father', 'mother', 'spouse', 'military_service', 'birth_place']
        for field in text_fields:
            if data.get(field):
                cleaned_text = data[field].strip()
                if cleaned_text:
                    validated[field] = cleaned_text
                else:
                    validated[field] = None

        return validated
    
    def _create_error_result(self, error_message: str) -> Dict[str, Any]:
        """
        建立錯誤結果
        
        Args:
            error_message: 錯誤訊息
            
        Returns:
            錯誤結果字典
        """
        return {
            "name": None,
            "id_number": None,
            "address": None,
            "confidence": 0.0,
            "error": error_message,
            "timestamp": datetime.now().isoformat()
        }
    
    def save_result(self, result: Dict[str, Any], filename: str = None) -> str:
        """
        儲存分析結果
        
        Args:
            result: 分析結果
            filename: 檔案名稱（可選）
            
        Returns:
            儲存的檔案路徑
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"id_analysis_{timestamp}.json"
        
        filepath = self.output_dir / filename
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            
            logger.info(f"分析結果已儲存: {filepath}")
            return str(filepath)
            
        except Exception as e:
            logger.error(f"儲存結果失敗: {e}")
            raise
    
    def analyze_batch(self, image_dir: str) -> Dict[str, Dict[str, Any]]:
        """
        批次分析多張身分證圖片
        
        Args:
            image_dir: 圖片目錄路徑
            
        Returns:
            批次分析結果
        """
        image_dir = Path(image_dir)
        if not image_dir.exists():
            logger.error(f"圖片目錄不存在: {image_dir}")
            return {}
        
        results = {}
        image_extensions = {'.jpg', '.jpeg', '.png', '.webp'}
        
        for image_file in image_dir.iterdir():
            if image_file.suffix.lower() in image_extensions:
                logger.info(f"處理圖片: {image_file.name}")
                result = self.extract_id_info(str(image_file))
                results[image_file.name] = result
        
        # 儲存批次結果
        batch_filename = f"batch_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        self.save_result(results, batch_filename)
        
        logger.info(f"批次分析完成，共處理 {len(results)} 張圖片")
        return results

def main():
    """主函數 - 命令列介面"""
    import argparse

    parser = argparse.ArgumentParser(description='身分證件資料分析器')
    parser.add_argument('image_path', help='身分證圖片路徑')
    parser.add_argument('--output', '-o', help='輸出檔案名稱')
    parser.add_argument('--batch', '-b', action='store_true', help='批次處理模式')
    parser.add_argument('--compare', '-c', action='store_true', help='比較 Gemini、OpenAI 和 Claude 結果')
    parser.add_argument('--openai-only', action='store_true', help='僅使用 OpenAI 分析')
    parser.add_argument('--gemini-only', action='store_true', help='僅使用 Gemini 分析')
    parser.add_argument('--claude-only', action='store_true', help='僅使用 Claude 分析')
    parser.add_argument('--side', choices=['front', 'back', 'auto'], default='auto', help='指定身分證面別 (front=正面, back=背面, auto=自動檢測)')

    args = parser.parse_args()
    
    # 載入環境變數
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        logger.warning("python-dotenv 未安裝，跳過 .env 檔案載入")
    
    try:
        analyzer = IDCardAnalyzer()
        
        if args.batch:
            # 批次處理
            results = analyzer.analyze_batch(args.image_path)
            print(f"✅ 批次分析完成，共處理 {len(results)} 張圖片")
        elif args.compare:
            # 比較模式
            comparison_result = analyzer.compare_analysis_results(args.image_path, args.side)

            # 儲存比較結果
            output_file = analyzer.save_result(comparison_result, args.output or f"comparison_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")

            # 顯示比較結果
            print("=" * 70)
            print("身分證分析三方比較結果")
            print("=" * 70)

            # 確定檢測到的面別
            detected_side = None
            for result_key in ['gemini_result', 'openai_result', 'claude_result']:
                if comparison_result.get(result_key) and comparison_result[result_key].get('detected_side'):
                    detected_side = comparison_result[result_key]['detected_side']
                    break

            print(f"\n� 檢測面別: {'正面' if detected_side == 'front' else '背面' if detected_side == 'back' else '未知'}")

            # 根據面別確定要顯示的欄位
            if detected_side == 'front':
                display_fields = [
                    ("姓名", "name"),
                    ("身分證號碼", "id_number"),
                    ("出生年月日", "birth_date"),
                    ("發證日期", "issue_date"),
                    ("性別", "gender")
                ]
            else:
                display_fields = [
                    ("父親", "father"),
                    ("母親", "mother"),
                    ("配偶", "spouse"),
                    ("役別", "military_service"),
                    ("出生地", "birth_place"),
                    ("住址", "address")
                ]

            # Gemini 結果
            if comparison_result.get('gemini_result'):
                gemini = comparison_result['gemini_result']
                print("\n🔵 Gemini 結果:")
                for label, field in display_fields:
                    print(f"   {label}: {gemini.get(field, 'N/A')}")
                print(f"   信心度: {gemini.get('confidence', 0):.2f}")
                if gemini.get('error'):
                    print(f"   錯誤: {gemini['error']}")

            # OpenAI 結果
            if comparison_result.get('openai_result'):
                openai = comparison_result['openai_result']
                print("\n🟠 OpenAI 結果:")
                for label, field in display_fields:
                    print(f"   {label}: {openai.get(field, 'N/A')}")
                print(f"   信心度: {openai.get('confidence', 0):.2f}")
                if openai.get('error'):
                    print(f"   錯誤: {openai['error']}")

            # Claude 結果
            if comparison_result.get('claude_result'):
                claude = comparison_result['claude_result']
                print("\n🟣 Claude 結果:")
                for label, field in display_fields:
                    print(f"   {label}: {claude.get(field, 'N/A')}")
                print(f"   信心度: {claude.get('confidence', 0):.2f}")
                if claude.get('error'):
                    print(f"   錯誤: {claude['error']}")

            # 比較分析
            if comparison_result.get('comparison'):
                comp = comparison_result['comparison']
                print("\n📊 多方比較分析:")
                print(f"   參與比較: {', '.join(comp['sources'])}")
                print(f"   整體一致性: {comp['consensus']['overall_agreement_rate']:.1%}")
                print(f"   平均相似度: {comp['consensus']['average_similarity']:.2f}")
                print(f"   推薦: {comp['recommendation']['message']}")

                # 欄位比較詳情
                print("\n📋 欄位比較:")
                for field, details in comp['fields_comparison'].items():
                    consensus = "✅" if details['consensus_value'] else "❌"
                    print(f"   {field}: {consensus} (一致性: {details['agreement_rate']:.1%})")
                    if details['values']:
                        for source, value in details['values'].items():
                            print(f"      {source}: {value}")

            print(f"\n結果已儲存至: {output_file}")

        elif args.openai_only:
            # 僅使用 OpenAI
            result = analyzer.extract_id_info_openai(args.image_path)

            # 儲存結果
            output_file = analyzer.save_result(result, args.output)

            # 顯示結果
            print("=" * 50)
            print("OpenAI 身分證分析結果")
            print("=" * 50)
            print(f"姓名: {result.get('name', 'N/A')}")
            print(f"身分證號碼: {result.get('id_number', 'N/A')}")
            print(f"地址: {result.get('address', 'N/A')}")
            print(f"信心度: {result.get('confidence', 0):.2f}")

            if result.get('error'):
                print(f"錯誤: {result['error']}")

            print(f"\n結果已儲存至: {output_file}")

        elif args.claude_only:
            # 僅使用 Claude
            result = analyzer.extract_id_info_claude(args.image_path)

            # 儲存結果
            output_file = analyzer.save_result(result, args.output)

            # 顯示結果
            print("=" * 50)
            print("Claude 身分證分析結果")
            print("=" * 50)
            print(f"姓名: {result.get('name', 'N/A')}")
            print(f"身分證號碼: {result.get('id_number', 'N/A')}")
            print(f"地址: {result.get('address', 'N/A')}")
            print(f"信心度: {result.get('confidence', 0):.2f}")

            if result.get('error'):
                print(f"錯誤: {result['error']}")

            print(f"\n結果已儲存至: {output_file}")

        else:
            # 預設使用 Gemini（或可用的 API）
            if args.gemini_only or not analyzer.openai_api_key:
                result = analyzer.extract_id_info(args.image_path, args.side)
                api_name = "Gemini"
            else:
                # 如果沒有指定，優先使用 Gemini
                result = analyzer.extract_id_info(args.image_path, args.side)
                api_name = "Gemini"

            # 儲存結果
            output_file = analyzer.save_result(result, args.output)

            # 顯示結果
            print("=" * 50)
            print(f"{api_name} 身分證分析結果")
            print("=" * 50)

            # 根據檢測到的面別顯示不同欄位
            detected_side = result.get('detected_side', 'unknown')
            print(f"檢測面別: {'正面' if detected_side == 'front' else '背面' if detected_side == 'back' else '未知'}")

            if detected_side == 'front':
                # 正面欄位
                print(f"姓名: {result.get('name', 'N/A')}")
                print(f"身分證號碼: {result.get('id_number', 'N/A')}")
                print(f"出生年月日: {result.get('birth_date', 'N/A')}")
                print(f"發證日期: {result.get('issue_date', 'N/A')}")
                print(f"性別: {result.get('gender', 'N/A')}")
            elif detected_side == 'back':
                # 背面欄位
                print(f"父親: {result.get('father', 'N/A')}")
                print(f"母親: {result.get('mother', 'N/A')}")
                print(f"配偶: {result.get('spouse', 'N/A')}")
                print(f"役別: {result.get('military_service', 'N/A')}")
                print(f"出生地: {result.get('birth_place', 'N/A')}")
                print(f"住址: {result.get('address', 'N/A')}")
            else:
                # 通用顯示
                for field in ['name', 'id_number', 'address', 'birth_date', 'gender', 'father', 'mother']:
                    if result.get(field):
                        print(f"{field}: {result[field]}")

            print(f"信心度: {result.get('confidence', 0):.2f}")

            if result.get('error'):
                print(f"錯誤: {result['error']}")

            print(f"\n結果已儲存至: {output_file}")
            
    except Exception as e:
        logger.error(f"程式執行失敗: {e}")
        print(f"❌ 執行失敗: {e}")

if __name__ == "__main__":
    main()
