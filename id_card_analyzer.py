#!/usr/bin/env python3
"""
台灣身分證正反面分析器
專門處理身分證正面和背面的不同欄位
"""

import os
import argparse
from pathlib import Path
from id_analyzer import IDCardAnalyzer
import logging

# 設定日誌
os.makedirs('logs', exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/id_card_analyzer.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def analyze_front_side(analyzer: IDCardAnalyzer, image_path: str, output_file: str = None):
    """分析身分證正面"""
    print("🔍 分析身分證正面...")
    result = analyzer.extract_id_info(image_path, side="front")
    
    # 儲存結果
    if not output_file:
        output_file = f"front_analysis_{Path(image_path).stem}.json"
    saved_file = analyzer.save_result(result, output_file)
    
    # 顯示結果
    print("=" * 60)
    print("身分證正面分析結果")
    print("=" * 60)
    
    if result.get('error'):
        print(f"❌ 分析失敗: {result['error']}")
        return result
    
    print(f"📄 圖片: {image_path}")
    print(f"🤖 來源: {result.get('source', 'unknown').upper()}")
    print(f"📊 信心度: {result.get('confidence', 0):.2f}")
    print()
    
    # 正面欄位
    fields = [
        ("姓名", "name"),
        ("身分證號碼", "id_number"),
        ("出生年月日", "birth_date"),
        ("發證日期", "issue_date"),
        ("性別", "gender")
    ]
    
    for label, field in fields:
        value = result.get(field, 'N/A')
        confidence = result.get('details', {}).get(f'{field}_confidence', 0)
        status = "✅" if value and value != 'N/A' else "❌"
        print(f"{status} {label}: {value} (信心度: {confidence:.2f})")
    
    print(f"\n💾 結果已儲存至: {saved_file}")
    return result

def analyze_back_side(analyzer: IDCardAnalyzer, image_path: str, output_file: str = None):
    """分析身分證背面"""
    print("🔍 分析身分證背面...")
    result = analyzer.extract_id_info(image_path, side="back")
    
    # 儲存結果
    if not output_file:
        output_file = f"back_analysis_{Path(image_path).stem}.json"
    saved_file = analyzer.save_result(result, output_file)
    
    # 顯示結果
    print("=" * 60)
    print("身分證背面分析結果")
    print("=" * 60)
    
    if result.get('error'):
        print(f"❌ 分析失敗: {result['error']}")
        return result
    
    print(f"📄 圖片: {image_path}")
    print(f"🤖 來源: {result.get('source', 'unknown').upper()}")
    print(f"📊 信心度: {result.get('confidence', 0):.2f}")
    print()
    
    # 背面欄位
    fields = [
        ("父親", "father"),
        ("母親", "mother"),
        ("配偶", "spouse"),
        ("役別", "military_service"),
        ("出生地", "birth_place"),
        ("住址", "address")
    ]
    
    for label, field in fields:
        value = result.get(field, 'N/A')
        confidence = result.get('details', {}).get(f'{field}_confidence', 0)
        status = "✅" if value and value != 'N/A' else "❌"
        print(f"{status} {label}: {value} (信心度: {confidence:.2f})")
    
    print(f"\n💾 結果已儲存至: {saved_file}")
    return result

def analyze_both_sides(analyzer: IDCardAnalyzer, front_path: str, back_path: str, output_file: str = None):
    """分析身分證正反兩面並合併結果"""
    print("🔍 分析身分證正反兩面...")
    
    # 分析正面
    front_result = analyzer.extract_id_info(front_path, side="front")
    
    # 分析背面
    back_result = analyzer.extract_id_info(back_path, side="back")
    
    # 合併結果
    merged_result = {
        "front_analysis": front_result,
        "back_analysis": back_result,
        "merged_data": {},
        "timestamp": front_result.get('timestamp'),
        "analysis_type": "both_sides"
    }
    
    # 合併所有欄位
    all_fields = {}
    
    # 從正面取得的欄位
    front_fields = ["name", "id_number", "birth_date", "issue_date", "gender"]
    for field in front_fields:
        if front_result.get(field):
            all_fields[field] = front_result[field]
    
    # 從背面取得的欄位
    back_fields = ["father", "mother", "spouse", "military_service", "birth_place", "address"]
    for field in back_fields:
        if back_result.get(field):
            all_fields[field] = back_result[field]
    
    merged_result["merged_data"] = all_fields
    
    # 計算整體信心度
    front_conf = front_result.get('confidence', 0)
    back_conf = back_result.get('confidence', 0)
    merged_result["overall_confidence"] = (front_conf + back_conf) / 2
    
    # 儲存結果
    if not output_file:
        front_stem = Path(front_path).stem
        back_stem = Path(back_path).stem
        output_file = f"complete_analysis_{front_stem}_{back_stem}.json"
    
    saved_file = analyzer.save_result(merged_result, output_file)
    
    # 顯示結果
    print("=" * 70)
    print("身分證完整分析結果")
    print("=" * 70)
    
    print(f"📄 正面圖片: {front_path}")
    print(f"📄 背面圖片: {back_path}")
    print(f"📊 整體信心度: {merged_result['overall_confidence']:.2f}")
    print()
    
    # 顯示所有欄位
    field_labels = {
        "name": "姓名",
        "id_number": "身分證號碼", 
        "birth_date": "出生年月日",
        "issue_date": "發證日期",
        "gender": "性別",
        "father": "父親",
        "mother": "母親",
        "spouse": "配偶",
        "military_service": "役別",
        "birth_place": "出生地",
        "address": "住址"
    }
    
    print("📋 完整資料:")
    for field, value in all_fields.items():
        label = field_labels.get(field, field)
        status = "✅" if value and value != 'N/A' else "❌"
        print(f"{status} {label}: {value}")
    
    print(f"\n💾 結果已儲存至: {saved_file}")
    return merged_result

def main():
    """主函數"""
    parser = argparse.ArgumentParser(description='台灣身分證正反面分析器')
    parser.add_argument('--front', help='身分證正面圖片路徑')
    parser.add_argument('--back', help='身分證背面圖片路徑')
    parser.add_argument('--both', nargs=2, metavar=('FRONT', 'BACK'), help='同時分析正反面 (正面路徑 背面路徑)')
    parser.add_argument('--output', '-o', help='輸出檔案名稱')
    parser.add_argument('--api', choices=['gemini', 'openai', 'claude'], default='gemini', help='選擇使用的 API')
    
    args = parser.parse_args()
    
    # 檢查參數
    if not any([args.front, args.back, args.both]):
        parser.error("請指定 --front、--back 或 --both 參數")
    
    try:
        # 載入環境變數
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        print("⚠️  python-dotenv 未安裝，跳過 .env 檔案載入")
    
    try:
        # 初始化分析器
        analyzer = IDCardAnalyzer("id_analysis_results")
        print("✅ 身分證分析器初始化成功")
        
        if args.both:
            # 分析正反兩面
            front_path, back_path = args.both
            
            # 檢查檔案是否存在
            if not Path(front_path).exists():
                print(f"❌ 正面圖片不存在: {front_path}")
                return
            if not Path(back_path).exists():
                print(f"❌ 背面圖片不存在: {back_path}")
                return
            
            analyze_both_sides(analyzer, front_path, back_path, args.output)
            
        elif args.front:
            # 分析正面
            if not Path(args.front).exists():
                print(f"❌ 正面圖片不存在: {args.front}")
                return
            
            analyze_front_side(analyzer, args.front, args.output)
            
        elif args.back:
            # 分析背面
            if not Path(args.back).exists():
                print(f"❌ 背面圖片不存在: {args.back}")
                return
            
            analyze_back_side(analyzer, args.back, args.output)
        
        print("\n✅ 分析完成！")
        
    except Exception as e:
        print(f"❌ 分析失敗: {e}")
        logger.error(f"分析過程發生錯誤: {e}")

if __name__ == "__main__":
    main()
